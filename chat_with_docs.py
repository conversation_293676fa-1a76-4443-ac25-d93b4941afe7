from fastapi import <PERSON>AP<PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List
import os
from dotenv import load_dotenv
import google.generativeai as genai
from pinecone import Pinecone
import json
import re
import shutil

# Importar funções do upload_to_pinecone.py
from upload_to_pinecone import chunk_text_with_tiktoken, process_pdf_with_images, get_embedding, pc, index, genai, generation_config, text_model, vision_model, pinecone_api_key, index_name, pdf_folder

# Carregar variáveis de ambiente
load_dotenv()

# Inicializar FastAPI
app = FastAPI()

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],  # URLs do frontend
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configurar clientes

# Health check endpoint
@app.get("/health")
async def health_check():
    """Endpoint para verificar se o backend está funcionando"""
    return {
        "status": "healthy",
        "message": "Backend RAGPinecone está funcionando",
        "version": "1.0.0"
    }

class Query(BaseModel):
    question: str
    chat_history: List[dict] = []

@app.post("/api/upload_pdf")
async def upload_pdf(file: UploadFile = File(...)):
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Apenas arquivos PDF são permitidos.")

    file_location = os.path.join(pdf_folder, file.filename)
    try:
        with open(file_location, "wb+") as file_object:
            shutil.copyfileobj(file.file, file_object)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao salvar o arquivo: {e}")

    try:
        # Processar o PDF e gerar chunks
        content_chunks = process_pdf_with_images(file_location)
        all_chunks = []

        for i, chunk in enumerate(content_chunks):
            chunk_text = chunk['content']
            chunk_type = chunk['type']
            page_num = chunk['page']

            if chunk_type == 'text':
                splits = chunk_text_with_tiktoken(chunk_text)
                for j, split_text in enumerate(splits):
                    safe_id = f"{file.filename}-p{page_num}-{j}"
                    all_chunks.append({
                        "id": safe_id,
                        "text": split_text,
                        "metadata": {
                            "source": file.filename,
                            "page": page_num,
                            "type": "text",
                            "text": split_text
                        }
                    })
            else:  # Image description
                safe_id = f"{file.filename}-p{page_num}-img{chunk.get('image_num', i)}"
                all_chunks.append({
                    "id": safe_id,
                    "text": chunk_text,
                    "metadata": {
                        "source": file.filename,
                        "page": page_num,
                        "type": "image_description",
                        "text": chunk_text
                    }
                })

        # Gerar embeddings e upsert no Pinecone
        vectors_to_upsert = []
        for chunk_data in all_chunks:
            embedding = get_embedding(chunk_data['text'], genai, text_model)
            if embedding:
                vectors_to_upsert.append({
                    "id": chunk_data['id'],
                    "values": embedding,
                    "metadata": chunk_data['metadata']
                })

        if vectors_to_upsert:
            index.upsert(vectors=vectors_to_upsert)
            print(f"Upserted {len(vectors_to_upsert)} vectors for {file.filename}")

        return {"message": f"Arquivo {file.filename} processado e embeds criados com sucesso!"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao processar o PDF e criar embeds: {e}")

@app.post("/api/chat")
async def chat_with_docs(query: Query):
    try:
        # Verificar se é uma pergunta sobre listar documentos
        question_lower = query.question.lower()
        is_document_list_query = any(phrase in question_lower for phrase in [
            "que documentos tens", "quais documentos", "lista documentos",
            "documentos disponíveis", "ficheiros tens", "que ficheiros",
            "lista ficheiros", "todos os documentos", "todos os ficheiros"
        ])

        # Recuperar contexto relevante do Pinecone
        try:
            question_embedding = genai.embed_content(
                model="models/embedding-001",
                content=query.question
            )
        except Exception as e:
            print(f"Erro ao gerar embedding: {e}")
            raise HTTPException(status_code=500, detail=f"Erro ao gerar embedding para a pergunta: {e}")

        try:
            # Se for pergunta sobre documentos, aumentar significativamente o top_k
            top_k_value = 50 if is_document_list_query else 8

            results = index.query(
                vector=question_embedding["embedding"],
                top_k=top_k_value,
                include_metadata=True
            )
        except Exception as e:
            print(f"Erro ao consultar Pinecone: {e}")
            raise HTTPException(status_code=500, detail=f"Erro ao consultar o Pinecone: {e}")

        # Para perguntas sobre documentos, extrair lista única de documentos
        if is_document_list_query:
            # Extrair nomes únicos de documentos
            unique_documents = set()
            document_info = {}

            for match in results['matches']:
                source = match['metadata']['source']
                unique_documents.add(source)
                if source not in document_info:
                    document_info[source] = {
                        'text': match['metadata'].get('text', ''),
                        'type': match['metadata'].get('type', 'text')
                    }

            # Construir contexto focado nos documentos únicos
            context = "\n".join([
                f"Documento disponível: '{doc}' - Tipo: {document_info[doc]['type']} - Conteúdo: {document_info[doc]['text'][:200]}..."
                for doc in sorted(unique_documents)
            ])
        else:
            # Extrair texto dos documentos mais relevantes (comportamento normal)
            context = "\n".join([
                f"Do documento '{match['metadata']['source']}' ({match['metadata'].get('type', 'text')}): {match['metadata'].get('text', '')}"
                for match in results['matches']
            ])
        
        # Construir histórico de conversa para contexto
        chat_context = ""
        if query.chat_history:
            chat_context = "\n".join([
                f"Utilizador: {msg.get('user', '')}\nAssistente: {msg.get('assistant', '')}"
                for msg in query.chat_history[-3:]  # Últimas 3 mensagens para contexto
            ])

        # Construir o prompt para gerar resposta natural e conversacional
        if is_document_list_query:
            prompt = f"""
            Você é um assistente inteligente e conversacional especializado em ajudar com documentos. O utilizador está a perguntar especificamente sobre que documentos tem disponíveis.

            DOCUMENTOS DISPONÍVEIS:
            {context}

            HISTÓRICO DA CONVERSA:
            {chat_context}

            PERGUNTA ATUAL: {query.question}

            INSTRUÇÕES ESPECÍFICAS PARA LISTAR DOCUMENTOS:
            - Liste TODOS os documentos únicos que encontrar no contexto
            - Para cada documento, forneça o nome completo do ficheiro
            - Se possível, adicione uma breve descrição do conteúdo de cada documento
            - Use uma lista numerada clara e bem formatada
            - Seja completo - não omita nenhum documento
            - Mantenha um tom amigável e conversacional
            - Use emojis ocasionalmente para tornar a resposta mais amigável

            FORMATAÇÃO:
            - Use listas numeradas: 1., 2., 3., etc.
            - Use **negrito** para os nomes dos documentos
            - Adicione uma linha em branco entre cada documento para melhor legibilidade
            - No final, pergunte se quer saber mais sobre algum documento específico

            Responda agora listando TODOS os documentos disponíveis:
            """
        else:
            prompt = f"""
            Você é um assistente inteligente e conversacional especializado em ajudar com documentos. Responda de forma natural, amigável e útil.

            CONTEXTO DOS DOCUMENTOS:
            {context}

            HISTÓRICO DA CONVERSA:
            {chat_context}

            PERGUNTA ATUAL: {query.question}

            INSTRUÇÕES:
            - Responda de forma natural e conversacional, como se fosse uma conversa entre amigos
            - Se cumprimentarem, cumprimente de volta de forma amigável
            - Para perguntas gerais (como "olá", "como estás"), responda naturalmente e ofereça ajuda com os documentos
            - Quando falar sobre documentos, seja específico mas mantenha um tom conversacional
            - Use expressões naturais como "Pelo que vejo no documento...", "De acordo com as informações que tenho...", "Interessante pergunta! No documento..."
            - Se não souber algo, diga de forma natural: "Hmm, não encontro essa informação nos documentos que tenho acesso"
            - Mantenha as respostas informativas mas acessíveis
            - Use emojis ocasionalmente para tornar a conversa mais amigável (mas sem exagerar)

            FORMATAÇÃO:
            - Quando listar documentos, use listas numeradas com espaçamento adequado
            - Separe cada documento com uma linha em branco
            - Use **negrito** para destacar nomes de documentos e informações importantes
            - Organize a informação de forma clara e estruturada

            Responda agora de forma natural e útil:
            """

        # Gerar resposta com configurações mais permissivas
        try:
            # Verificar se o modelo está disponível
            if text_model is None:
                raise Exception("Modelo de texto não disponível")

            # Configuração específica para esta chamada
            generation_config_chat = {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "candidate_count": 1,
                "max_output_tokens": 2048,
            }

            safety_settings_permissive = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
            ]

            response = text_model.generate_content(
                prompt,
                generation_config=generation_config_chat,
                safety_settings=safety_settings_permissive,
                stream=False
            )

            # Verificar se a resposta é válida
            if not response or not hasattr(response, 'text') or not response.text:
                print(f"Resposta vazia ou inválida do modelo.")
                if hasattr(response, 'candidates') and response.candidates:
                    candidate = response.candidates[0]
                    print(f"Finish reason: {getattr(candidate, 'finish_reason', 'unknown')}")
                    if hasattr(candidate, 'safety_ratings'):
                        print(f"Safety ratings: {candidate.safety_ratings}")

                response_text = "Desculpe, não consegui gerar uma resposta adequada. Podes tentar reformular a pergunta? 🤔"
            else:
                response_text = response.text.strip()

        except Exception as e:
            print(f"Erro ao gerar conteúdo com o modelo de texto: {e}")

            # Sistema de fallback inteligente
            if "429" in str(e) or "quota" in str(e).lower():
                # Fallback para quota excedida - fornecer resposta baseada no contexto
                if is_document_list_query:
                    # Para perguntas sobre documentos, extrair lista dos metadados
                    unique_docs = set()
                    for match in results['matches']:
                        unique_docs.add(match['metadata']['source'])

                    if unique_docs:
                        doc_list = "\n".join([f"📄 **{doc}**" for doc in sorted(unique_docs)])
                        response_text = f"""Olá! 😊

Tenho os seguintes documentos disponíveis para te ajudar:

{doc_list}

Podes fazer-me qualquer pergunta sobre o conteúdo destes documentos!

*Nota: Estou a usar um modo simplificado devido a limitações temporárias da API, mas posso ainda aceder ao conteúdo dos documentos.*"""
                    else:
                        response_text = "Olá! 😊 Parece que não tenho documentos carregados no momento. Podes carregar alguns PDFs para começarmos a conversar sobre eles!"
                else:
                    # Para outras perguntas, tentar fornecer informação básica do contexto
                    if context and len(context.strip()) > 0:
                        # Extrair informações básicas do contexto
                        docs_mentioned = set()
                        for match in results['matches']:
                            docs_mentioned.add(match['metadata']['source'])

                        if docs_mentioned:
                            docs_list = ", ".join(sorted(docs_mentioned))
                            response_text = f"""Olá! 😊

Encontrei informações relacionadas com a tua pergunta nos seguintes documentos: {docs_list}

*Nota: Estou a usar um modo simplificado devido a limitações temporárias da API. Para respostas mais detalhadas, tenta novamente mais tarde ou reformula a pergunta de forma mais específica.*

Posso tentar ajudar-te de outra forma? 🤔"""
                        else:
                            response_text = "Olá! 😊 Não encontrei informações específicas sobre isso nos documentos que tenho. Podes tentar uma pergunta diferente ou mais específica? 🤔"
                    else:
                        response_text = "Olá! 😊 Não encontrei informações relevantes nos documentos para responder à tua pergunta. Podes tentar reformular ou fazer uma pergunta diferente? 🤔"
            else:
                # Outros tipos de erro
                response_text = "Hmm, parece que estou com algumas dificuldades técnicas no momento. Podes tentar novamente? 😅"
        
        return {
            "resposta_formatada": response_text,
            "fontes": []
        }

    except Exception as e:
        print(f"Erro geral na função chat_with_docs: {e}")
        return {
            "resposta_formatada": f"Desculpe, ocorreu um erro inesperado no servidor: {e}",
            "fontes": []
        }
