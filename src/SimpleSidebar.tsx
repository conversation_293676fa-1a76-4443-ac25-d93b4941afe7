import React from 'react';
import {
  MessageSquare,
  FileText,
  Code2,
  Settings,
  Zap
} from 'lucide-react';

interface SimpleSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const SimpleSidebar: React.FC<SimpleSidebarProps> = ({ activeSection, onSectionChange }) => {
  const menuItems = [
    { id: 'chat', label: 'Chat RAG', icon: MessageSquare },
    { id: 'upload', label: 'Documentos', icon: FileText },
    { id: 'embed', label: 'Integração', icon: Code2 },
    { id: 'settings', label: 'Configurações', icon: Settings },
  ];

  return (
    <div
      style={{
        width: '240px',
        height: '100vh',
        backgroundColor: '#0a0a0a',
        borderRight: '1px solid #1a1a1a',
        display: 'flex',
        flexDirection: 'column',
        flexShrink: 0,
        position: 'relative'
      }}
    >
      {/* Header */}
      <div style={{
        padding: '16px 20px',
        borderBottom: '1px solid #1a1a1a',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Zap size={16} color="#00d4aa" />
          <span style={{
            color: '#ffffff',
            fontSize: '14px',
            fontWeight: '600'
          }}>
            RAGPinecone
          </span>
        </div>
        <div style={{
          backgroundColor: '#1a1a1a',
          padding: '2px 6px',
          borderRadius: '8px',
          fontSize: '10px',
          color: '#888',
          fontWeight: '500'
        }}>
          Free
        </div>
      </div>

      {/* Menu Items */}
      <div style={{ flex: 1, padding: '8px 0', overflowY: 'auto' }}>
        <nav style={{ padding: '0 16px' }}>
          {menuItems.map((item) => {
            const isActive = activeSection === item.id;
            const IconComponent = item.icon;

            return (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id)}
                style={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '10px 12px',
                  textAlign: 'left',
                  backgroundColor: isActive ? '#1a1a1a' : 'transparent',
                  color: isActive ? '#ffffff' : '#888888',
                  borderRadius: '12px',
                  marginBottom: '2px',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '13px',
                  fontWeight: '500',
                  transition: 'all 0.15s ease'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = '#1a1a1a';
                    e.currentTarget.style.color = '#ffffff';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#888888';
                  }
                }}
              >
                <IconComponent
                  size={16}
                  style={{
                    marginRight: '12px',
                    color: isActive ? '#00d4aa' : '#888888'
                  }}
                />
                {item.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Footer */}
      <div style={{
        borderTop: '1px solid #1a1a1a',
        padding: '12px 16px',
        backgroundColor: '#0a0a0a'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            style={{
              width: '24px',
              height: '24px',
              backgroundColor: '#1a1a1a',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span style={{ color: '#888', fontSize: '10px', fontWeight: '600' }}>U</span>
          </div>
          <div style={{ flex: 1 }}>
            <p style={{
              color: '#ffffff',
              fontSize: '12px',
              fontWeight: '500',
              margin: 0,
              lineHeight: '1.2'
            }}>
              Utilizador
            </p>
            <p style={{
              color: '#888',
              fontSize: '10px',
              margin: 0,
              lineHeight: '1.2'
            }}>
              Admin
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleSidebar;
