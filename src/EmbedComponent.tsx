import React, { useState } from 'react';

const EmbedComponent: React.FC = () => {
  const [embedCode, setEmbedCode] = useState('');
  const [apiUrl, setApiUrl] = useState('https://your-api-domain.com/api/chat');
  const [scriptUrl, setScriptUrl] = useState('https://your-domain.com/chatbot-embed.js');
  const [customization, setCustomization] = useState({
    width: '350px',
    height: '450px',
    primaryColor: '#007bff'
  });

  const generateEmbedCode = () => {
    const code = `<!-- RAGPinecone Chatbot Embed -->
<div id="rag-chatbot-container"></div>
<script src="${scriptUrl}"></script>
<script>
  // Inicializar o chatbot quando a página carregar
  document.addEventListener('DOMContentLoaded', function() {
    initRagChatbot('rag-chatbot-container', '${apiUrl}');
  });
</script>

<style>
  /* Personalização do chatbot */
  .rag-chatbot-widget {
    width: ${customization.width} !important;
    height: ${customization.height} !important;
  }

  .rag-chatbot-header {
    background-color: ${customization.primaryColor} !important;
  }

  .rag-chatbot-input-area button {
    background-color: ${customization.primaryColor} !important;
  }
</style>`;
    setEmbedCode(code);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(embedCode);
    alert('Código copiado para a área de transferência!');
  };

  return (
    <>
      <style>
        {`
          input[type="color"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: transparent;
            border: none;
            cursor: pointer;
          }

          input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
            border: none;
            border-radius: 12px;
            background: transparent;
          }

          input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 12px;
            box-shadow: none;
          }

          input[type="color"]::-moz-color-swatch {
            border: none;
            border-radius: 12px;
          }

          input[type="color"]::-moz-focus-inner {
            border: 0;
          }
        `}
      </style>
      <div style={{
        padding: '32px',
        maxWidth: '1200px',
        margin: '0 auto',
        color: 'white',
        backgroundColor: '#0f0f0f',
        minHeight: '100vh'
      }}>
      {/* Header */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{ fontSize: '32px', fontWeight: 'bold', marginBottom: '16px', color: '#ffffff' }}>
          Integração
        </h1>
        <p style={{ color: '#888888', fontSize: '18px' }}>
          Integre o chatbot RAGPinecone no seu website com apenas algumas linhas de código.
        </p>
      </div>

      {/* Configuration */}
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '32px', marginBottom: '32px' }}>
        <div style={{ backgroundColor: '#1a1a1a', borderRadius: '16px', padding: '24px', border: '1px solid #2a2a2a' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px' }}>
            Configuração
          </h2>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#ffffff', marginBottom: '8px' }}>
                URL do Script
              </label>
              <input
                type="text"
                value={scriptUrl}
                onChange={(e) => setScriptUrl(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  backgroundColor: '#0f0f0f',
                  border: '1px solid #2a2a2a',
                  borderRadius: '12px',
                  color: 'white',
                  outline: 'none'
                }}
                placeholder="https://seusite.com/chatbot-embed.js"
              />
            </div>

            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#ffffff', marginBottom: '8px' }}>
                URL da API
              </label>
              <input
                type="text"
                value={apiUrl}
                onChange={(e) => setApiUrl(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  backgroundColor: '#0f0f0f',
                  border: '1px solid #2a2a2a',
                  borderRadius: '12px',
                  color: 'white',
                  outline: 'none'
                }}
                placeholder="https://seuapi.com/api/chat"
              />
            </div>

            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#ffffff', marginBottom: '8px' }}>
                Cor Principal
              </label>
              <input
                type="color"
                value={customization.primaryColor}
                onChange={(e) => setCustomization({...customization, primaryColor: e.target.value})}
                style={{
                  width: '100%',
                  height: '40px',
                  backgroundColor: '#0f0f0f',
                  border: '1px solid #2a2a2a',
                  borderRadius: '12px',
                  outline: 'none'
                }}
              />
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
              <div>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#ffffff', marginBottom: '8px' }}>
                  Largura
                </label>
                <input
                  type="text"
                  value={customization.width}
                  onChange={(e) => setCustomization({...customization, width: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    backgroundColor: '#0f0f0f',
                    border: '1px solid #2a2a2a',
                    borderRadius: '12px',
                    color: 'white',
                    outline: 'none'
                  }}
                  placeholder="350px"
                />
              </div>
              <div>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#ffffff', marginBottom: '8px' }}>
                  Altura
                </label>
                <input
                  type="text"
                  value={customization.height}
                  onChange={(e) => setCustomization({...customization, height: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    backgroundColor: '#0f0f0f',
                    border: '1px solid #2a2a2a',
                    borderRadius: '12px',
                    color: 'white',
                    outline: 'none'
                  }}
                  placeholder="450px"
                />
              </div>
            </div>

            <button
              onClick={generateEmbedCode}
              style={{
                width: '100%',
                backgroundColor: '#10b981',
                color: 'white',
                fontWeight: '500',
                padding: '12px 16px',
                borderRadius: '12px',
                border: 'none',
                cursor: 'pointer',
                marginTop: '16px'
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#059669'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#10b981'}
            >
              Gerar Código de Embed
            </button>
          </div>
        </div>

        {/* Preview */}
        <div style={{ backgroundColor: '#0f0f0f', borderRadius: '16px', padding: '24px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px' }}>
            Pré-visualização
          </h2>
          <div style={{
            backgroundColor: '#1a1a1a',
            borderRadius: '16px',
            padding: '16px',
            height: '256px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div style={{
              backgroundColor: '#2a2a2a',
              borderRadius: '16px',
              padding: '16px',
              border: `2px solid ${customization.primaryColor}`,
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                <div style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: customization.primaryColor,
                  marginRight: '8px'
                }}></div>
                <span style={{ color: 'white', fontSize: '14px' }}>
                  Assistente RAG
                </span>
              </div>
              <div style={{ fontSize: '12px', color: '#888888' }}>
                {customization.width} × {customization.height}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Generated Code */}
      {embedCode && (
        <div style={{ backgroundColor: '#0f0f0f', borderRadius: '16px', padding: '24px', marginTop: '32px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
            <h2 style={{ fontSize: '20px', fontWeight: '600' }}>Código de Embed</h2>
            <button
              onClick={copyToClipboard}
              style={{
                backgroundColor: '#10b981',
                color: 'white',
                fontWeight: '500',
                padding: '8px 16px',
                borderRadius: '12px',
                border: 'none',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#059669'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#10b981'}
            >
              Copiar Código
            </button>
          </div>
          <pre style={{
            backgroundColor: '#000000',
            borderRadius: '12px',
            padding: '16px',
            overflowX: 'auto',
            fontSize: '14px',
            color: '#10b981'
          }}>
            <code>{embedCode}</code>
          </pre>
        </div>
      )}

      {/* Instructions */}
      <div style={{ backgroundColor: '#0f0f0f', borderRadius: '16px', padding: '24px', marginTop: '32px' }}>
        <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px' }}>
          Instruções de Instalação
        </h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', color: '#ffffff' }}>
          <div>
            <h3 style={{ fontWeight: '500', color: 'white', marginBottom: '8px' }}>
              1. Gerar e Copiar o Código
            </h3>
            <p>Use o botão "Gerar Código de Embed" e depois "Copiar Código".</p>
          </div>
          <div>
            <h3 style={{ fontWeight: '500', color: 'white', marginBottom: '8px' }}>
              2. Configurar URLs
            </h3>
            <p>Configure os URLs corretos nos campos acima antes de gerar o código.</p>
          </div>
          <div>
            <h3 style={{ fontWeight: '500', color: 'white', marginBottom: '8px' }}>
              3. Integrar no Website
            </h3>
            <p>Cole o código HTML gerado na página onde pretende que o chatbot apareça.</p>
          </div>
          <div>
            <h3 style={{ fontWeight: '500', color: 'white', marginBottom: '8px' }}>
              4. Ficheiro JavaScript
            </h3>
            <p>
              Certifique-se de que o ficheiro <span style={{ backgroundColor: '#2a2a2a', padding: '2px 6px', borderRadius: '8px' }}>chatbot-embed.js</span> está acessível no URL configurado.
            </p>
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default EmbedComponent;
