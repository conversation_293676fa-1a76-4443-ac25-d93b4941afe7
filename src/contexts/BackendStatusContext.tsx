import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type BackendStatus = 'online' | 'offline' | 'checking';

interface BackendStatusContextType {
  status: BackendStatus;
  checkStatus: () => Promise<void>;
}

const BackendStatusContext = createContext<BackendStatusContextType | undefined>(undefined);

export const useBackendStatus = () => {
  const context = useContext(BackendStatusContext);
  if (context === undefined) {
    throw new Error('useBackendStatus must be used within a BackendStatusProvider');
  }
  return context;
};

interface BackendStatusProviderProps {
  children: ReactNode;
}

export const BackendStatusProvider: React.FC<BackendStatusProviderProps> = ({ children }) => {
  const [status, setStatus] = useState<BackendStatus>('checking');

  const checkStatus = async () => {
    try {
      setStatus('checking');
      
      // Tentar conectar ao backend
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 segundos timeout
      
      const response = await fetch('http://localhost:8000/health', {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        setStatus('online');
      } else {
        setStatus('offline');
      }
    } catch (error) {
      console.log('Backend status check failed:', error);
      setStatus('offline');
    }
  };

  // Verificar status ao carregar
  useEffect(() => {
    checkStatus();
    
    // Verificar status a cada 10 segundos
    const interval = setInterval(checkStatus, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const value = {
    status,
    checkStatus,
  };

  return (
    <BackendStatusContext.Provider value={value}>
      {children}
    </BackendStatusContext.Provider>
  );
};
