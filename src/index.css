@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100vh;
  width: 100vw;
  background-color: #0f0f0f !important;
  color: #ffffff !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}



/* Animações */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Estilos para mensagens do chat */
.chat-message {
  line-height: 1.6;
}

.chat-message p {
  margin: 0 0 16px 0;
}

.chat-message p:last-child {
  margin-bottom: 0;
}

.chat-message ol, .chat-message ul {
  margin: 12px 0 20px 0;
  padding-left: 24px;
}

.chat-message li {
  margin: 12px 0;
  line-height: 1.6;
  padding-left: 4px;
}

.chat-message li p {
  margin: 0 0 8px 0;
}

.chat-message li:last-child {
  margin-bottom: 0;
}

.chat-message h1, .chat-message h2, .chat-message h3 {
  margin: 20px 0 12px 0;
  line-height: 1.3;
}

.chat-message strong {
  font-weight: 600;
  color: #10b981;
}

.chat-message code {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Espaçamento específico para listas numeradas */
.chat-message ol li {
  margin: 20px 0;
  padding-left: 8px;
  border-left: 2px solid transparent;
  padding-left: 12px;
}

.chat-message ol li:hover {
  border-left-color: #10b981;
  background-color: rgba(16, 185, 129, 0.05);
  border-radius: 4px;
  padding-left: 12px;
  margin-left: -4px;
  padding-right: 8px;
}

.chat-message ol li::marker {
  font-weight: 700;
  color: #10b981;
  font-size: 1.1em;
}

/* Melhor espaçamento para parágrafos dentro de listas */
.chat-message ol li > p:first-child {
  margin-top: 0;
}

.chat-message ol li > p:last-child {
  margin-bottom: 0;
}

/* Separação visual entre itens de lista */
.chat-message ol li + li {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  padding-top: 20px;
}




