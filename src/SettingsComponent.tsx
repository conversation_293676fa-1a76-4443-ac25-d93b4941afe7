import React, { useState } from 'react';
import { Settings, Save, Key, Database, Globe } from 'lucide-react';

const SettingsComponent: React.FC = () => {
  const [apiKey, setApiKey] = useState('');
  const [pineconeIndex, setPineconeIndex] = useState('');
  const [environment, setEnvironment] = useState('');

  const handleSave = () => {
    // Implementar lógica de guardar configurações
    alert('Configurações guardadas com sucesso!');
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      height: '100%', 
      backgroundColor: '#0f0f0f' 
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '20px 24px',
        borderBottom: '1px solid #1a1a1a',
        backgroundColor: '#0f0f0f'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{
            width: '32px',
            height: '32px',
            backgroundColor: '#00d4aa',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Settings size={16} color="#000" />
          </div>
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: '#ffffff', margin: 0 }}>Settings</h2>
            <span style={{ fontSize: '14px', color: '#888888' }}>Configure as definições do sistema</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div style={{ flex: 1, padding: '32px', overflowY: 'auto' }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          {/* API Configuration */}
          <div style={{
            backgroundColor: '#1a1a1a',
            borderRadius: '12px',
            padding: '24px',
            marginBottom: '24px',
            border: '1px solid #2a2a2a'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '20px' }}>
              <Key size={20} color="#00d4aa" />
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#ffffff', margin: 0 }}>
                Configuração da API
              </h3>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#ffffff', 
                marginBottom: '8px' 
              }}>
                Chave da API OpenAI
              </label>
              <input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-..."
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#0f0f0f',
                  border: '1px solid #2a2a2a',
                  borderRadius: '8px',
                  color: '#ffffff',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>

          {/* Pinecone Configuration */}
          <div style={{
            backgroundColor: '#1a1a1a',
            borderRadius: '12px',
            padding: '24px',
            marginBottom: '24px',
            border: '1px solid #2a2a2a'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '20px' }}>
              <Database size={20} color="#00d4aa" />
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#ffffff', margin: 0 }}>
                Configuração Pinecone
              </h3>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#ffffff', 
                marginBottom: '8px' 
              }}>
                Nome do Índice
              </label>
              <input
                type="text"
                value={pineconeIndex}
                onChange={(e) => setPineconeIndex(e.target.value)}
                placeholder="rag-documents"
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#0f0f0f',
                  border: '1px solid #2a2a2a',
                  borderRadius: '8px',
                  color: '#ffffff',
                  fontSize: '14px'
                }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#ffffff', 
                marginBottom: '8px' 
              }}>
                Ambiente
              </label>
              <input
                type="text"
                value={environment}
                onChange={(e) => setEnvironment(e.target.value)}
                placeholder="us-east-1-aws"
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#0f0f0f',
                  border: '1px solid #2a2a2a',
                  borderRadius: '8px',
                  color: '#ffffff',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>

          {/* System Information */}
          <div style={{
            backgroundColor: '#1a1a1a',
            borderRadius: '12px',
            padding: '24px',
            marginBottom: '24px',
            border: '1px solid #2a2a2a'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '20px' }}>
              <Globe size={20} color="#00d4aa" />
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#ffffff', margin: 0 }}>
                Informações do Sistema
              </h3>
            </div>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
              <div>
                <span style={{ fontSize: '14px', color: '#888888' }}>Versão:</span>
                <p style={{ fontSize: '16px', color: '#ffffff', margin: '4px 0 0 0', fontWeight: '500' }}>
                  1.0.0
                </p>
              </div>
              <div>
                <span style={{ fontSize: '14px', color: '#888888' }}>Status:</span>
                <p style={{ fontSize: '16px', color: '#00d4aa', margin: '4px 0 0 0', fontWeight: '500' }}>
                  Online
                </p>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <button
            onClick={handleSave}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 24px',
              backgroundColor: '#00d4aa',
              color: '#000',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#00c49a';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#00d4aa';
            }}
          >
            <Save size={16} />
            Guardar Configurações
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsComponent;
