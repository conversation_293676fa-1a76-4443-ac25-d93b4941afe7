# RAGPinecone - Sistema de Chat com Documentos

![React](https://img.shields.io/badge/React-18.2.0-blue)
![TypeScript](https://img.shields.io/badge/TypeScript-5.8.3-blue)
![Vite](https://img.shields.io/badge/Vite-4.3.2-purple)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.3.0-cyan)
![Python](https://img.shields.io/badge/Python-3.11+-green)
![FastAPI](https://img.shields.io/badge/FastAPI-Latest-green)

## 📋 Descrição

RAGPinecone é uma aplicação completa de chat com documentos que utiliza Retrieval-Augmented Generation (RAG) para permitir conversas inteligentes baseadas em documentos PDF. O sistema combina um frontend moderno em React/TypeScript com um backend Python robusto, oferecendo uma experiência de utilizador fluida e profissional.

## ✨ Funcionalidades Principais

### 🎯 Interface Completa
- **Dashboard com Sidebar**: Interface moderna com navegação lateral limpa
- **Chat Inteligente**: Conversas naturais com documentos processados
- **Upload de Documentos**: Sistema de upload com processamento automático de PDFs
- **Gerador de Embed**: Ferramenta para integrar o chatbot em websites externos
- **Exportação de Conversas**: Download das conversas em formato texto

### 🔧 Tecnologias Frontend
- **React 18** com TypeScript para type safety
- **Vite** para desenvolvimento rápido e build otimizado
- **TailwindCSS** para estilização moderna e responsiva
- **Lucide React** para ícones consistentes e modernos
- **React Markdown** para renderização de respostas formatadas

### ⚡ Backend Robusto
- **FastAPI** para APIs rápidas e documentadas
- **Pinecone** para armazenamento vetorial e busca semântica
- **Google Gemini** para geração de respostas inteligentes
- **Processamento de PDFs** com extração de texto e imagens
- **OCR** para documentos digitalizados

## 🚀 Instalação e Configuração

### Pré-requisitos
- Node.js 18+
- Python 3.11+
- Conta Pinecone
- Chave API Google Gemini

### 1. Configuração do Backend

```bash
# Instalar dependências Python
pip install -r requirements.txt

# Configurar variáveis de ambiente
cp .env.example .env
# Editar .env com suas chaves API
```

### 2. Configuração do Frontend

```bash
# Instalar dependências Node.js
npm install

# Iniciar servidor de desenvolvimento
npm run dev
```

### 3. Iniciar o Sistema

```bash
# Terminal 1: Backend
uvicorn chat_with_docs:app --reload --port 8000

# Terminal 2: Frontend
npm run dev
```

## 🏗️ Arquitetura do Sistema

### Frontend (React/TypeScript)
```
src/
├── App.tsx              # Componente principal com roteamento
├── ChatComponent.tsx    # Interface de chat principal
├── UploadComponent.tsx  # Sistema de upload de documentos
├── EmbedComponent.tsx   # Gerador de código embed
├── SimpleSidebar.tsx    # Navegação lateral
└── index.css           # Estilos globais e animações
```

### Backend (Python/FastAPI)
```
├── chat_with_docs.py       # API principal e lógica de chat
├── upload_to_pinecone.py   # Processamento e indexação de documentos
├── pdf_image_processor.py  # Extração de texto e imagens de PDFs
└── requirements.txt        # Dependências Python
```

## 📡 Endpoints da API

### Chat
- **POST** `/api/chat`
  - Processa perguntas e retorna respostas baseadas nos documentos
  - Suporte a histórico de conversa
  - Respostas formatadas em Markdown

### Upload
- **POST** `/api/upload_pdf`
  - Upload e processamento automático de PDFs
  - Extração de texto e imagens
  - Indexação automática no Pinecone

## 🎨 Design e UX

### Esquema de Cores
- **Fundo Principal**: `#030712` (Cinza muito escuro)
- **Fundo Secundário**: `#111827` (Cinza escuro)
- **Accent**: `#10b981` (Verde esmeralda)
- **Texto**: `#f9fafb` (Branco suave)

### Componentes Principais
- **Sidebar Responsiva**: Navegação intuitiva entre funcionalidades
- **Chat Interface**: Design moderno com indicadores de carregamento
- **Upload Zone**: Drag & drop com feedback visual
- **Embed Generator**: Configuração visual para integração externa

## 🔧 Funcionalidades Técnicas

### Processamento de Documentos
- Extração de texto de PDFs
- OCR para documentos digitalizados
- Processamento de imagens incorporadas
- Chunking inteligente para indexação

### Sistema de Chat
- Busca semântica no Pinecone
- Geração de respostas contextuais
- Histórico de conversa persistente
- Formatação Markdown automática

### Integração Externa
- Código embed personalizável
- API RESTful documentada
- CORS configurado para integração web

## 📱 Responsividade

O sistema é totalmente responsivo e funciona em:
- **Desktop**: Interface completa com sidebar
- **Tablet**: Layout adaptado com navegação otimizada
- **Mobile**: Interface compacta e touch-friendly

## 🔒 Segurança

- Validação de tipos de ficheiro (apenas PDFs)
- Sanitização de inputs
- Gestão segura de chaves API via variáveis de ambiente
- CORS configurado adequadamente

## 📈 Performance

- **Frontend**: Build otimizado com Vite
- **Backend**: APIs assíncronas com FastAPI
- **Caching**: Respostas otimizadas do Pinecone
- **Lazy Loading**: Componentes carregados sob demanda
