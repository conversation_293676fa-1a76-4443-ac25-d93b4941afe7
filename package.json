{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^4.3.2"}}