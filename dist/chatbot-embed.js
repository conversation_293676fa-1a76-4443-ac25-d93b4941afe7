(function() {
    window.initRagChatbot = function(containerId, apiUrl) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Container with ID '${containerId}' not found.`);
            return;
        }

        // Create chat interface elements
        container.innerHTML = `
            <style>
                .rag-chatbot-widget {
                    font-family: Arial, sans-serif;
                    border: 1px solid #ccc;
                    border-radius: 8px;
                    width: 350px;
                    height: 450px;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }
                .rag-chatbot-header {
                    background-color: #007bff;
                    color: white;
                    padding: 10px;
                    text-align: center;
                    font-size: 1.1em;
                }
                .rag-chatbot-messages {
                    flex-grow: 1;
                    padding: 10px;
                    overflow-y: auto;
                    background-color: #f9f9f9;
                }
                .rag-chatbot-message {
                    margin-bottom: 8px;
                    padding: 6px 10px;
                    border-radius: 12px;
                    max-width: 80%;
                    word-wrap: break-word;
                }
                .rag-chatbot-message.user {
                    background-color: #dcf8c6;
                    margin-left: auto;
                }
                .rag-chatbot-message.bot {
                    background-color: #e2e2e2;
                    margin-right: auto;
                }
                .rag-chatbot-input-area {
                    display: flex;
                    padding: 10px;
                    border-top: 1px solid #ccc;
                    background-color: #fff;
                }
                .rag-chatbot-input-area input {
                    flex-grow: 1;
                    padding: 8px;
                    border: 1px solid #ccc;
                    border-radius: 15px;
                    margin-right: 8px;
                }
                .rag-chatbot-input-area button {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    border-radius: 15px;
                    padding: 8px 15px;
                    cursor: pointer;
                }
                .rag-chatbot-input-area button:disabled {
                    background-color: #cccccc;
                    cursor: not-allowed;
                }
            </style>
            <div class="rag-chatbot-widget">
                <div class="rag-chatbot-header">Chatbot de Documentos</div>
                <div class="rag-chatbot-messages"></div>
                <div class="rag-chatbot-input-area">
                    <input type="text" placeholder="Digite sua mensagem...">
                    <button>Enviar</button>
                </div>
            </div>
        `;

        const messagesDiv = container.querySelector('.rag-chatbot-messages');
        const inputElement = container.querySelector('.rag-chatbot-input-area input');
        const sendButton = container.querySelector('.rag-chatbot-input-area button');

        const addMessage = (text, sender) => {
            const messageElement = document.createElement('div');
            messageElement.classList.add('rag-chatbot-message', sender);
            messageElement.textContent = text;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        };

        const sendMessage = async () => {
            const text = inputElement.value.trim();
            if (text === '') return;

            addMessage(text, 'user');
            inputElement.value = '';
            sendButton.disabled = true;
            inputElement.disabled = true;

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ question: text, chat_history: [] }), // Simplified history for embed
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                // Assuming the backend returns a 'response' field or similar
                addMessage(data.response || JSON.stringify(data), 'bot');
            } catch (error) {
                console.error('Error sending message:', error);
                addMessage('Desculpe, houve um erro ao processar sua solicitação.', 'bot');
            } finally {
                sendButton.disabled = false;
                inputElement.disabled = false;
            }
        };

        sendButton.addEventListener('click', sendMessage);
        inputElement.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    };
})();