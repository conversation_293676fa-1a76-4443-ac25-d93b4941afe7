/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-border-style:solid;--tw-outline-style:solid}}}.visible{visibility:visible}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.relative{position:relative}.container{width:100%}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline-flex{display:inline-flex}.table{display:table}.flex-grow{flex-grow:1}.resize{resize:both}.border{border-style:var(--tw-border-style);border-width:1px}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition\!{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events!important;transition-timing-function:var(--tw-ease,ease)!important;transition-duration:var(--tw-duration,0s)!important}*{box-sizing:border-box;margin:0;padding:0}html,body,#root{width:100vw;height:100vh;font-family:Inter,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;color:#f9fafb!important;background-color:#030712!important}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes bounce{0%,80%,to{opacity:.5;transform:scale(0)}40%{opacity:1;transform:scale(1)}}.animate-spin{animation:1s linear infinite spin}.chat-message{line-height:1.6}.chat-message p{margin:0 0 16px}.chat-message p:last-child{margin-bottom:0}.chat-message ol,.chat-message ul{margin:12px 0 20px;padding-left:24px}.chat-message li{margin:12px 0;padding-left:4px;line-height:1.6}.chat-message li p{margin:0 0 8px}.chat-message li:last-child{margin-bottom:0}.chat-message h1,.chat-message h2,.chat-message h3{margin:20px 0 12px;line-height:1.3}.chat-message strong{color:#10b981;font-weight:600}.chat-message code{background-color:#ffffff1a;border-radius:4px;padding:2px 6px;font-family:Monaco,Menlo,Ubuntu Mono,monospace;font-size:.9em}.chat-message ol li{border-left:2px solid #0000;margin:20px 0;padding-left:12px}.chat-message ol li:hover{background-color:#10b9810d;border-left-color:#10b981;border-radius:4px;margin-left:-4px;padding-left:12px;padding-right:8px}.chat-message ol li::marker{color:#10b981;font-size:1.1em;font-weight:700}.chat-message ol li>p:first-child{margin-top:0}.chat-message ol li>p:last-child{margin-bottom:0}.chat-message ol li+li{border-top:1px solid #ffffff0d;padding-top:20px}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}
