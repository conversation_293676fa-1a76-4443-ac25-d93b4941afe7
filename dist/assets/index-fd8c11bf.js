(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const o of l.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function t(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(i){if(i.ep)return;i.ep=!0;const l=t(i);fetch(i.href,l)}})();var qi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},w={},Nh={get exports(){return w},set exports(e){w=e}},Sl={},le={},Fh={get exports(){return le},set exports(e){le=e}},H={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ti=Symbol.for("react.element"),Mh=Symbol.for("react.portal"),Oh=Symbol.for("react.fragment"),Bh=Symbol.for("react.strict_mode"),Uh=Symbol.for("react.profiler"),$h=Symbol.for("react.provider"),Hh=Symbol.for("react.context"),Vh=Symbol.for("react.forward_ref"),Wh=Symbol.for("react.suspense"),Qh=Symbol.for("react.memo"),qh=Symbol.for("react.lazy"),rs=Symbol.iterator;function Yh(e){return e===null||typeof e!="object"?null:(e=rs&&e[rs]||e["@@iterator"],typeof e=="function"?e:null)}var pf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},df=Object.assign,hf={};function ir(e,n,t){this.props=e,this.context=n,this.refs=hf,this.updater=t||pf}ir.prototype.isReactComponent={};ir.prototype.setState=function(e,n){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,n,"setState")};ir.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function mf(){}mf.prototype=ir.prototype;function Ou(e,n,t){this.props=e,this.context=n,this.refs=hf,this.updater=t||pf}var Bu=Ou.prototype=new mf;Bu.constructor=Ou;df(Bu,ir.prototype);Bu.isPureReactComponent=!0;var is=Array.isArray,gf=Object.prototype.hasOwnProperty,Uu={current:null},yf={key:!0,ref:!0,__self:!0,__source:!0};function xf(e,n,t){var r,i={},l=null,o=null;if(n!=null)for(r in n.ref!==void 0&&(o=n.ref),n.key!==void 0&&(l=""+n.key),n)gf.call(n,r)&&!yf.hasOwnProperty(r)&&(i[r]=n[r]);var u=arguments.length-2;if(u===1)i.children=t;else if(1<u){for(var a=Array(u),s=0;s<u;s++)a[s]=arguments[s+2];i.children=a}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)i[r]===void 0&&(i[r]=u[r]);return{$$typeof:ti,type:e,key:l,ref:o,props:i,_owner:Uu.current}}function Kh(e,n){return{$$typeof:ti,type:e.type,key:n,ref:e.ref,props:e.props,_owner:e._owner}}function $u(e){return typeof e=="object"&&e!==null&&e.$$typeof===ti}function Xh(e){var n={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(t){return n[t]})}var ls=/\/+/g;function Vl(e,n){return typeof e=="object"&&e!==null&&e.key!=null?Xh(""+e.key):n.toString(36)}function Ai(e,n,t,r,i){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(l){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ti:case Mh:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Vl(o,0):r,is(i)?(t="",e!=null&&(t=e.replace(ls,"$&/")+"/"),Ai(i,n,t,"",function(s){return s})):i!=null&&($u(i)&&(i=Kh(i,t+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(ls,"$&/")+"/")+e)),n.push(i)),1;if(o=0,r=r===""?".":r+":",is(e))for(var u=0;u<e.length;u++){l=e[u];var a=r+Vl(l,u);o+=Ai(l,n,t,a,i)}else if(a=Yh(e),typeof a=="function")for(e=a.call(e),u=0;!(l=e.next()).done;)l=l.value,a=r+Vl(l,u++),o+=Ai(l,n,t,a,i);else if(l==="object")throw n=String(e),Error("Objects are not valid as a React child (found: "+(n==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.");return o}function pi(e,n,t){if(e==null)return e;var r=[],i=0;return Ai(e,r,"","",function(l){return n.call(t,l,i++)}),r}function Gh(e){if(e._status===-1){var n=e._result;n=n(),n.then(function(t){(e._status===0||e._status===-1)&&(e._status=1,e._result=t)},function(t){(e._status===0||e._status===-1)&&(e._status=2,e._result=t)}),e._status===-1&&(e._status=0,e._result=n)}if(e._status===1)return e._result.default;throw e._result}var De={current:null},Di={transition:null},Zh={ReactCurrentDispatcher:De,ReactCurrentBatchConfig:Di,ReactCurrentOwner:Uu};H.Children={map:pi,forEach:function(e,n,t){pi(e,function(){n.apply(this,arguments)},t)},count:function(e){var n=0;return pi(e,function(){n++}),n},toArray:function(e){return pi(e,function(n){return n})||[]},only:function(e){if(!$u(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};H.Component=ir;H.Fragment=Oh;H.Profiler=Uh;H.PureComponent=Ou;H.StrictMode=Bh;H.Suspense=Wh;H.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zh;H.cloneElement=function(e,n,t){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=df({},e.props),i=e.key,l=e.ref,o=e._owner;if(n!=null){if(n.ref!==void 0&&(l=n.ref,o=Uu.current),n.key!==void 0&&(i=""+n.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(a in n)gf.call(n,a)&&!yf.hasOwnProperty(a)&&(r[a]=n[a]===void 0&&u!==void 0?u[a]:n[a])}var a=arguments.length-2;if(a===1)r.children=t;else if(1<a){u=Array(a);for(var s=0;s<a;s++)u[s]=arguments[s+2];r.children=u}return{$$typeof:ti,type:e.type,key:i,ref:l,props:r,_owner:o}};H.createContext=function(e){return e={$$typeof:Hh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:$h,_context:e},e.Consumer=e};H.createElement=xf;H.createFactory=function(e){var n=xf.bind(null,e);return n.type=e,n};H.createRef=function(){return{current:null}};H.forwardRef=function(e){return{$$typeof:Vh,render:e}};H.isValidElement=$u;H.lazy=function(e){return{$$typeof:qh,_payload:{_status:-1,_result:e},_init:Gh}};H.memo=function(e,n){return{$$typeof:Qh,type:e,compare:n===void 0?null:n}};H.startTransition=function(e){var n=Di.transition;Di.transition={};try{e()}finally{Di.transition=n}};H.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};H.useCallback=function(e,n){return De.current.useCallback(e,n)};H.useContext=function(e){return De.current.useContext(e)};H.useDebugValue=function(){};H.useDeferredValue=function(e){return De.current.useDeferredValue(e)};H.useEffect=function(e,n){return De.current.useEffect(e,n)};H.useId=function(){return De.current.useId()};H.useImperativeHandle=function(e,n,t){return De.current.useImperativeHandle(e,n,t)};H.useInsertionEffect=function(e,n){return De.current.useInsertionEffect(e,n)};H.useLayoutEffect=function(e,n){return De.current.useLayoutEffect(e,n)};H.useMemo=function(e,n){return De.current.useMemo(e,n)};H.useReducer=function(e,n,t){return De.current.useReducer(e,n,t)};H.useRef=function(e){return De.current.useRef(e)};H.useState=function(e){return De.current.useState(e)};H.useSyncExternalStore=function(e,n,t){return De.current.useSyncExternalStore(e,n,t)};H.useTransition=function(){return De.current.useTransition()};H.version="18.2.0";(function(e){e.exports=H})(Fh);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jh=le,em=Symbol.for("react.element"),nm=Symbol.for("react.fragment"),tm=Object.prototype.hasOwnProperty,rm=Jh.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,im={key:!0,ref:!0,__self:!0,__source:!0};function kf(e,n,t){var r,i={},l=null,o=null;t!==void 0&&(l=""+t),n.key!==void 0&&(l=""+n.key),n.ref!==void 0&&(o=n.ref);for(r in n)tm.call(n,r)&&!im.hasOwnProperty(r)&&(i[r]=n[r]);if(e&&e.defaultProps)for(r in n=e.defaultProps,n)i[r]===void 0&&(i[r]=n[r]);return{$$typeof:em,type:e,key:l,ref:o,props:i,_owner:rm.current}}Sl.Fragment=nm;Sl.jsx=kf;Sl.jsxs=kf;(function(e){e.exports=Sl})(Nh);var Ao={},lm={get exports(){return Ao},set exports(e){Ao=e}},Xe={},Do={},om={get exports(){return Do},set exports(e){Do=e}},vf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function n(A,O){var x=A.length;A.push(O);e:for(;0<x;){var Y=x-1>>>1,G=A[Y];if(0<i(G,O))A[Y]=O,A[x]=G,x=Y;else break e}}function t(A){return A.length===0?null:A[0]}function r(A){if(A.length===0)return null;var O=A[0],x=A.pop();if(x!==O){A[0]=x;e:for(var Y=0,G=A.length,k=G>>>1;Y<k;){var ye=2*(Y+1)-1,on=A[ye],re=ye+1,gn=A[re];if(0>i(on,x))re<G&&0>i(gn,on)?(A[Y]=gn,A[re]=x,Y=re):(A[Y]=on,A[ye]=x,Y=ye);else if(re<G&&0>i(gn,x))A[Y]=gn,A[re]=x,Y=re;else break e}}return O}function i(A,O){var x=A.sortIndex-O.sortIndex;return x!==0?x:A.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var o=Date,u=o.now();e.unstable_now=function(){return o.now()-u}}var a=[],s=[],f=1,c=null,p=3,d=!1,g=!1,v=!1,E=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(A){for(var O=t(s);O!==null;){if(O.callback===null)r(s);else if(O.startTime<=A)r(s),O.sortIndex=O.expirationTime,n(a,O);else break;O=t(s)}}function T(A){if(v=!1,y(A),!g)if(t(a)!==null)g=!0,he(P);else{var O=t(s);O!==null&&pe(T,O.startTime-A)}}function P(A,O){g=!1,v&&(v=!1,h(L),L=-1),d=!0;var x=p;try{for(y(O),c=t(a);c!==null&&(!(c.expirationTime>O)||A&&!D());){var Y=c.callback;if(typeof Y=="function"){c.callback=null,p=c.priorityLevel;var G=Y(c.expirationTime<=O);O=e.unstable_now(),typeof G=="function"?c.callback=G:c===t(a)&&r(a),y(O)}else r(a);c=t(a)}if(c!==null)var k=!0;else{var ye=t(s);ye!==null&&pe(T,ye.startTime-O),k=!1}return k}finally{c=null,p=x,d=!1}}var S=!1,_=null,L=-1,M=5,C=-1;function D(){return!(e.unstable_now()-C<M)}function N(){if(_!==null){var A=e.unstable_now();C=A;var O=!0;try{O=_(!0,A)}finally{O?q():(S=!1,_=null)}}else S=!1}var q;if(typeof m=="function")q=function(){m(N)};else if(typeof MessageChannel<"u"){var ee=new MessageChannel,V=ee.port2;ee.port1.onmessage=N,q=function(){V.postMessage(null)}}else q=function(){E(N,0)};function he(A){_=A,S||(S=!0,q())}function pe(A,O){L=E(function(){A(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(A){A.callback=null},e.unstable_continueExecution=function(){g||d||(g=!0,he(P))},e.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<A?Math.floor(1e3/A):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return t(a)},e.unstable_next=function(A){switch(p){case 1:case 2:case 3:var O=3;break;default:O=p}var x=p;p=O;try{return A()}finally{p=x}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(A,O){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var x=p;p=A;try{return O()}finally{p=x}},e.unstable_scheduleCallback=function(A,O,x){var Y=e.unstable_now();switch(typeof x=="object"&&x!==null?(x=x.delay,x=typeof x=="number"&&0<x?Y+x:Y):x=Y,A){case 1:var G=-1;break;case 2:G=250;break;case 5:G=**********;break;case 4:G=1e4;break;default:G=5e3}return G=x+G,A={id:f++,callback:O,priorityLevel:A,startTime:x,expirationTime:G,sortIndex:-1},x>Y?(A.sortIndex=x,n(s,A),t(a)===null&&A===t(s)&&(v?(h(L),L=-1):v=!0,pe(T,x-Y))):(A.sortIndex=G,n(a,A),g||d||(g=!0,he(P))),A},e.unstable_shouldYield=D,e.unstable_wrapCallback=function(A){var O=p;return function(){var x=p;p=O;try{return A.apply(this,arguments)}finally{p=x}}}})(vf);(function(e){e.exports=vf})(om);/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wf=le,Ke=Do;function I(e){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+e,t=1;t<arguments.length;t++)n+="&args[]="+encodeURIComponent(arguments[t]);return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Sf=new Set,Fr={};function St(e,n){Xt(e,n),Xt(e+"Capture",n)}function Xt(e,n){for(Fr[e]=n,e=0;e<n.length;e++)Sf.add(n[e])}var Ln=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),jo=Object.prototype.hasOwnProperty,um=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,os={},us={};function am(e){return jo.call(us,e)?!0:jo.call(os,e)?!1:um.test(e)?us[e]=!0:(os[e]=!0,!1)}function sm(e,n,t,r){if(t!==null&&t.type===0)return!1;switch(typeof n){case"function":case"symbol":return!0;case"boolean":return r?!1:t!==null?!t.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function cm(e,n,t,r){if(n===null||typeof n>"u"||sm(e,n,t,r))return!0;if(r)return!1;if(t!==null)switch(t.type){case 3:return!n;case 4:return n===!1;case 5:return isNaN(n);case 6:return isNaN(n)||1>n}return!1}function je(e,n,t,r,i,l,o){this.acceptsBooleans=n===2||n===3||n===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=t,this.propertyName=e,this.type=n,this.sanitizeURL=l,this.removeEmptyString=o}var Ee={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ee[e]=new je(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var n=e[0];Ee[n]=new je(n,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ee[e]=new je(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ee[e]=new je(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ee[e]=new je(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ee[e]=new je(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ee[e]=new je(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ee[e]=new je(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ee[e]=new je(e,5,!1,e.toLowerCase(),null,!1,!1)});var Hu=/[\-:]([a-z])/g;function Vu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var n=e.replace(Hu,Vu);Ee[n]=new je(n,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var n=e.replace(Hu,Vu);Ee[n]=new je(n,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var n=e.replace(Hu,Vu);Ee[n]=new je(n,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ee[e]=new je(e,1,!1,e.toLowerCase(),null,!1,!1)});Ee.xlinkHref=new je("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ee[e]=new je(e,1,!1,e.toLowerCase(),null,!0,!0)});function Wu(e,n,t,r){var i=Ee.hasOwnProperty(n)?Ee[n]:null;(i!==null?i.type!==0:r||!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(cm(n,t,i,r)&&(t=null),r||i===null?am(n)&&(t===null?e.removeAttribute(n):e.setAttribute(n,""+t)):i.mustUseProperty?e[i.propertyName]=t===null?i.type===3?!1:"":t:(n=i.attributeName,r=i.attributeNamespace,t===null?e.removeAttribute(n):(i=i.type,t=i===3||i===4&&t===!0?"":""+t,r?e.setAttributeNS(r,n,t):e.setAttribute(n,t))))}var jn=wf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,di=Symbol.for("react.element"),Lt=Symbol.for("react.portal"),Rt=Symbol.for("react.fragment"),Qu=Symbol.for("react.strict_mode"),No=Symbol.for("react.profiler"),Cf=Symbol.for("react.provider"),Ef=Symbol.for("react.context"),qu=Symbol.for("react.forward_ref"),Fo=Symbol.for("react.suspense"),Mo=Symbol.for("react.suspense_list"),Yu=Symbol.for("react.memo"),Bn=Symbol.for("react.lazy"),Tf=Symbol.for("react.offscreen"),as=Symbol.iterator;function fr(e){return e===null||typeof e!="object"?null:(e=as&&e[as]||e["@@iterator"],typeof e=="function"?e:null)}var ce=Object.assign,Wl;function wr(e){if(Wl===void 0)try{throw Error()}catch(t){var n=t.stack.trim().match(/\n( *(at )?)/);Wl=n&&n[1]||""}return`
`+Wl+e}var Ql=!1;function ql(e,n){if(!e||Ql)return"";Ql=!0;var t=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch(s){var r=s}Reflect.construct(e,[],n)}else{try{n.call()}catch(s){r=s}e.call(n.prototype)}else{try{throw Error()}catch(s){r=s}e()}}catch(s){if(s&&r&&typeof s.stack=="string"){for(var i=s.stack.split(`
`),l=r.stack.split(`
`),o=i.length-1,u=l.length-1;1<=o&&0<=u&&i[o]!==l[u];)u--;for(;1<=o&&0<=u;o--,u--)if(i[o]!==l[u]){if(o!==1||u!==1)do if(o--,u--,0>u||i[o]!==l[u]){var a=`
`+i[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=u);break}}}finally{Ql=!1,Error.prepareStackTrace=t}return(e=e?e.displayName||e.name:"")?wr(e):""}function fm(e){switch(e.tag){case 5:return wr(e.type);case 16:return wr("Lazy");case 13:return wr("Suspense");case 19:return wr("SuspenseList");case 0:case 2:case 15:return e=ql(e.type,!1),e;case 11:return e=ql(e.type.render,!1),e;case 1:return e=ql(e.type,!0),e;default:return""}}function Oo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Rt:return"Fragment";case Lt:return"Portal";case No:return"Profiler";case Qu:return"StrictMode";case Fo:return"Suspense";case Mo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ef:return(e.displayName||"Context")+".Consumer";case Cf:return(e._context.displayName||"Context")+".Provider";case qu:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Yu:return n=e.displayName||null,n!==null?n:Oo(e.type)||"Memo";case Bn:n=e._payload,e=e._init;try{return Oo(e(n))}catch{}}return null}function pm(e){var n=e.type;switch(e.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=n.render,e=e.displayName||e.name||"",n.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Oo(n);case 8:return n===Qu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function et(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function zf(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function dm(e){var n=zf(e)?"checked":"value",t=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),r=""+e[n];if(!e.hasOwnProperty(n)&&typeof t<"u"&&typeof t.get=="function"&&typeof t.set=="function"){var i=t.get,l=t.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,l.call(this,o)}}),Object.defineProperty(e,n,{enumerable:t.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function hi(e){e._valueTracker||(e._valueTracker=dm(e))}function Pf(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var t=n.getValue(),r="";return e&&(r=zf(e)?e.checked?"true":"false":e.value),e=r,e!==t?(n.setValue(e),!0):!1}function Yi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Bo(e,n){var t=n.checked;return ce({},n,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:t??e._wrapperState.initialChecked})}function ss(e,n){var t=n.defaultValue==null?"":n.defaultValue,r=n.checked!=null?n.checked:n.defaultChecked;t=et(n.value!=null?n.value:t),e._wrapperState={initialChecked:r,initialValue:t,controlled:n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null}}function If(e,n){n=n.checked,n!=null&&Wu(e,"checked",n,!1)}function Uo(e,n){If(e,n);var t=et(n.value),r=n.type;if(t!=null)r==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+t):e.value!==""+t&&(e.value=""+t);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}n.hasOwnProperty("value")?$o(e,n.type,t):n.hasOwnProperty("defaultValue")&&$o(e,n.type,et(n.defaultValue)),n.checked==null&&n.defaultChecked!=null&&(e.defaultChecked=!!n.defaultChecked)}function cs(e,n,t){if(n.hasOwnProperty("value")||n.hasOwnProperty("defaultValue")){var r=n.type;if(!(r!=="submit"&&r!=="reset"||n.value!==void 0&&n.value!==null))return;n=""+e._wrapperState.initialValue,t||n===e.value||(e.value=n),e.defaultValue=n}t=e.name,t!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,t!==""&&(e.name=t)}function $o(e,n,t){(n!=="number"||Yi(e.ownerDocument)!==e)&&(t==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+t&&(e.defaultValue=""+t))}var Sr=Array.isArray;function Ht(e,n,t,r){if(e=e.options,n){n={};for(var i=0;i<t.length;i++)n["$"+t[i]]=!0;for(t=0;t<e.length;t++)i=n.hasOwnProperty("$"+e[t].value),e[t].selected!==i&&(e[t].selected=i),i&&r&&(e[t].defaultSelected=!0)}else{for(t=""+et(t),n=null,i=0;i<e.length;i++){if(e[i].value===t){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}n!==null||e[i].disabled||(n=e[i])}n!==null&&(n.selected=!0)}}function Ho(e,n){if(n.dangerouslySetInnerHTML!=null)throw Error(I(91));return ce({},n,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function fs(e,n){var t=n.value;if(t==null){if(t=n.children,n=n.defaultValue,t!=null){if(n!=null)throw Error(I(92));if(Sr(t)){if(1<t.length)throw Error(I(93));t=t[0]}n=t}n==null&&(n=""),t=n}e._wrapperState={initialValue:et(t)}}function _f(e,n){var t=et(n.value),r=et(n.defaultValue);t!=null&&(t=""+t,t!==e.value&&(e.value=t),n.defaultValue==null&&e.defaultValue!==t&&(e.defaultValue=t)),r!=null&&(e.defaultValue=""+r)}function ps(e){var n=e.textContent;n===e._wrapperState.initialValue&&n!==""&&n!==null&&(e.value=n)}function bf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Vo(e,n){return e==null||e==="http://www.w3.org/1999/xhtml"?bf(n):e==="http://www.w3.org/2000/svg"&&n==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var mi,Lf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(n,t,r,i){MSApp.execUnsafeLocalFunction(function(){return e(n,t,r,i)})}:e}(function(e,n){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=n;else{for(mi=mi||document.createElement("div"),mi.innerHTML="<svg>"+n.valueOf().toString()+"</svg>",n=mi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;n.firstChild;)e.appendChild(n.firstChild)}});function Mr(e,n){if(n){var t=e.firstChild;if(t&&t===e.lastChild&&t.nodeType===3){t.nodeValue=n;return}}e.textContent=n}var Tr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hm=["Webkit","ms","Moz","O"];Object.keys(Tr).forEach(function(e){hm.forEach(function(n){n=n+e.charAt(0).toUpperCase()+e.substring(1),Tr[n]=Tr[e]})});function Rf(e,n,t){return n==null||typeof n=="boolean"||n===""?"":t||typeof n!="number"||n===0||Tr.hasOwnProperty(e)&&Tr[e]?(""+n).trim():n+"px"}function Af(e,n){e=e.style;for(var t in n)if(n.hasOwnProperty(t)){var r=t.indexOf("--")===0,i=Rf(t,n[t],r);t==="float"&&(t="cssFloat"),r?e.setProperty(t,i):e[t]=i}}var mm=ce({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wo(e,n){if(n){if(mm[e]&&(n.children!=null||n.dangerouslySetInnerHTML!=null))throw Error(I(137,e));if(n.dangerouslySetInnerHTML!=null){if(n.children!=null)throw Error(I(60));if(typeof n.dangerouslySetInnerHTML!="object"||!("__html"in n.dangerouslySetInnerHTML))throw Error(I(61))}if(n.style!=null&&typeof n.style!="object")throw Error(I(62))}}function Qo(e,n){if(e.indexOf("-")===-1)return typeof n.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var qo=null;function Ku(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Yo=null,Vt=null,Wt=null;function ds(e){if(e=li(e)){if(typeof Yo!="function")throw Error(I(280));var n=e.stateNode;n&&(n=Pl(n),Yo(e.stateNode,e.type,n))}}function Df(e){Vt?Wt?Wt.push(e):Wt=[e]:Vt=e}function jf(){if(Vt){var e=Vt,n=Wt;if(Wt=Vt=null,ds(e),n)for(e=0;e<n.length;e++)ds(n[e])}}function Nf(e,n){return e(n)}function Ff(){}var Yl=!1;function Mf(e,n,t){if(Yl)return e(n,t);Yl=!0;try{return Nf(e,n,t)}finally{Yl=!1,(Vt!==null||Wt!==null)&&(Ff(),jf())}}function Or(e,n){var t=e.stateNode;if(t===null)return null;var r=Pl(t);if(r===null)return null;t=r[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(t&&typeof t!="function")throw Error(I(231,n,typeof t));return t}var Ko=!1;if(Ln)try{var pr={};Object.defineProperty(pr,"passive",{get:function(){Ko=!0}}),window.addEventListener("test",pr,pr),window.removeEventListener("test",pr,pr)}catch{Ko=!1}function gm(e,n,t,r,i,l,o,u,a){var s=Array.prototype.slice.call(arguments,3);try{n.apply(t,s)}catch(f){this.onError(f)}}var zr=!1,Ki=null,Xi=!1,Xo=null,ym={onError:function(e){zr=!0,Ki=e}};function xm(e,n,t,r,i,l,o,u,a){zr=!1,Ki=null,gm.apply(ym,arguments)}function km(e,n,t,r,i,l,o,u,a){if(xm.apply(this,arguments),zr){if(zr){var s=Ki;zr=!1,Ki=null}else throw Error(I(198));Xi||(Xi=!0,Xo=s)}}function Ct(e){var n=e,t=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,n.flags&4098&&(t=n.return),e=n.return;while(e)}return n.tag===3?t:null}function Of(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function hs(e){if(Ct(e)!==e)throw Error(I(188))}function vm(e){var n=e.alternate;if(!n){if(n=Ct(e),n===null)throw Error(I(188));return n!==e?null:e}for(var t=e,r=n;;){var i=t.return;if(i===null)break;var l=i.alternate;if(l===null){if(r=i.return,r!==null){t=r;continue}break}if(i.child===l.child){for(l=i.child;l;){if(l===t)return hs(i),e;if(l===r)return hs(i),n;l=l.sibling}throw Error(I(188))}if(t.return!==r.return)t=i,r=l;else{for(var o=!1,u=i.child;u;){if(u===t){o=!0,t=i,r=l;break}if(u===r){o=!0,r=i,t=l;break}u=u.sibling}if(!o){for(u=l.child;u;){if(u===t){o=!0,t=l,r=i;break}if(u===r){o=!0,r=l,t=i;break}u=u.sibling}if(!o)throw Error(I(189))}}if(t.alternate!==r)throw Error(I(190))}if(t.tag!==3)throw Error(I(188));return t.stateNode.current===t?e:n}function Bf(e){return e=vm(e),e!==null?Uf(e):null}function Uf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var n=Uf(e);if(n!==null)return n;e=e.sibling}return null}var $f=Ke.unstable_scheduleCallback,ms=Ke.unstable_cancelCallback,wm=Ke.unstable_shouldYield,Sm=Ke.unstable_requestPaint,de=Ke.unstable_now,Cm=Ke.unstable_getCurrentPriorityLevel,Xu=Ke.unstable_ImmediatePriority,Hf=Ke.unstable_UserBlockingPriority,Gi=Ke.unstable_NormalPriority,Em=Ke.unstable_LowPriority,Vf=Ke.unstable_IdlePriority,Cl=null,wn=null;function Tm(e){if(wn&&typeof wn.onCommitFiberRoot=="function")try{wn.onCommitFiberRoot(Cl,e,void 0,(e.current.flags&128)===128)}catch{}}var pn=Math.clz32?Math.clz32:Im,zm=Math.log,Pm=Math.LN2;function Im(e){return e>>>=0,e===0?32:31-(zm(e)/Pm|0)|0}var gi=64,yi=4194304;function Cr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Zi(e,n){var t=e.pendingLanes;if(t===0)return 0;var r=0,i=e.suspendedLanes,l=e.pingedLanes,o=t&268435455;if(o!==0){var u=o&~i;u!==0?r=Cr(u):(l&=o,l!==0&&(r=Cr(l)))}else o=t&~i,o!==0?r=Cr(o):l!==0&&(r=Cr(l));if(r===0)return 0;if(n!==0&&n!==r&&!(n&i)&&(i=r&-r,l=n&-n,i>=l||i===16&&(l&4194240)!==0))return n;if(r&4&&(r|=t&16),n=e.entangledLanes,n!==0)for(e=e.entanglements,n&=r;0<n;)t=31-pn(n),i=1<<t,r|=e[t],n&=~i;return r}function _m(e,n){switch(e){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function bm(e,n){for(var t=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-pn(l),u=1<<o,a=i[o];a===-1?(!(u&t)||u&r)&&(i[o]=_m(u,n)):a<=n&&(e.expiredLanes|=u),l&=~u}}function Go(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Wf(){var e=gi;return gi<<=1,!(gi&4194240)&&(gi=64),e}function Kl(e){for(var n=[],t=0;31>t;t++)n.push(e);return n}function ri(e,n,t){e.pendingLanes|=n,n!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,n=31-pn(n),e[n]=t}function Lm(e,n){var t=e.pendingLanes&~n;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=n,e.mutableReadLanes&=n,e.entangledLanes&=n,n=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<t;){var i=31-pn(t),l=1<<i;n[i]=0,r[i]=-1,e[i]=-1,t&=~l}}function Gu(e,n){var t=e.entangledLanes|=n;for(e=e.entanglements;t;){var r=31-pn(t),i=1<<r;i&n|e[r]&n&&(e[r]|=n),t&=~i}}var X=0;function Qf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var qf,Zu,Yf,Kf,Xf,Zo=!1,xi=[],Qn=null,qn=null,Yn=null,Br=new Map,Ur=new Map,$n=[],Rm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function gs(e,n){switch(e){case"focusin":case"focusout":Qn=null;break;case"dragenter":case"dragleave":qn=null;break;case"mouseover":case"mouseout":Yn=null;break;case"pointerover":case"pointerout":Br.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ur.delete(n.pointerId)}}function dr(e,n,t,r,i,l){return e===null||e.nativeEvent!==l?(e={blockedOn:n,domEventName:t,eventSystemFlags:r,nativeEvent:l,targetContainers:[i]},n!==null&&(n=li(n),n!==null&&Zu(n)),e):(e.eventSystemFlags|=r,n=e.targetContainers,i!==null&&n.indexOf(i)===-1&&n.push(i),e)}function Am(e,n,t,r,i){switch(n){case"focusin":return Qn=dr(Qn,e,n,t,r,i),!0;case"dragenter":return qn=dr(qn,e,n,t,r,i),!0;case"mouseover":return Yn=dr(Yn,e,n,t,r,i),!0;case"pointerover":var l=i.pointerId;return Br.set(l,dr(Br.get(l)||null,e,n,t,r,i)),!0;case"gotpointercapture":return l=i.pointerId,Ur.set(l,dr(Ur.get(l)||null,e,n,t,r,i)),!0}return!1}function Gf(e){var n=ft(e.target);if(n!==null){var t=Ct(n);if(t!==null){if(n=t.tag,n===13){if(n=Of(t),n!==null){e.blockedOn=n,Xf(e.priority,function(){Yf(t)});return}}else if(n===3&&t.stateNode.current.memoizedState.isDehydrated){e.blockedOn=t.tag===3?t.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ji(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var t=Jo(e.domEventName,e.eventSystemFlags,n[0],e.nativeEvent);if(t===null){t=e.nativeEvent;var r=new t.constructor(t.type,t);qo=r,t.target.dispatchEvent(r),qo=null}else return n=li(t),n!==null&&Zu(n),e.blockedOn=t,!1;n.shift()}return!0}function ys(e,n,t){ji(e)&&t.delete(n)}function Dm(){Zo=!1,Qn!==null&&ji(Qn)&&(Qn=null),qn!==null&&ji(qn)&&(qn=null),Yn!==null&&ji(Yn)&&(Yn=null),Br.forEach(ys),Ur.forEach(ys)}function hr(e,n){e.blockedOn===n&&(e.blockedOn=null,Zo||(Zo=!0,Ke.unstable_scheduleCallback(Ke.unstable_NormalPriority,Dm)))}function $r(e){function n(i){return hr(i,e)}if(0<xi.length){hr(xi[0],e);for(var t=1;t<xi.length;t++){var r=xi[t];r.blockedOn===e&&(r.blockedOn=null)}}for(Qn!==null&&hr(Qn,e),qn!==null&&hr(qn,e),Yn!==null&&hr(Yn,e),Br.forEach(n),Ur.forEach(n),t=0;t<$n.length;t++)r=$n[t],r.blockedOn===e&&(r.blockedOn=null);for(;0<$n.length&&(t=$n[0],t.blockedOn===null);)Gf(t),t.blockedOn===null&&$n.shift()}var Qt=jn.ReactCurrentBatchConfig,Ji=!0;function jm(e,n,t,r){var i=X,l=Qt.transition;Qt.transition=null;try{X=1,Ju(e,n,t,r)}finally{X=i,Qt.transition=l}}function Nm(e,n,t,r){var i=X,l=Qt.transition;Qt.transition=null;try{X=4,Ju(e,n,t,r)}finally{X=i,Qt.transition=l}}function Ju(e,n,t,r){if(Ji){var i=Jo(e,n,t,r);if(i===null)lo(e,n,r,el,t),gs(e,r);else if(Am(i,e,n,t,r))r.stopPropagation();else if(gs(e,r),n&4&&-1<Rm.indexOf(e)){for(;i!==null;){var l=li(i);if(l!==null&&qf(l),l=Jo(e,n,t,r),l===null&&lo(e,n,r,el,t),l===i)break;i=l}i!==null&&r.stopPropagation()}else lo(e,n,r,null,t)}}var el=null;function Jo(e,n,t,r){if(el=null,e=Ku(r),e=ft(e),e!==null)if(n=Ct(e),n===null)e=null;else if(t=n.tag,t===13){if(e=Of(n),e!==null)return e;e=null}else if(t===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null);return el=e,null}function Zf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Cm()){case Xu:return 1;case Hf:return 4;case Gi:case Em:return 16;case Vf:return 536870912;default:return 16}default:return 16}}var Vn=null,ea=null,Ni=null;function Jf(){if(Ni)return Ni;var e,n=ea,t=n.length,r,i="value"in Vn?Vn.value:Vn.textContent,l=i.length;for(e=0;e<t&&n[e]===i[e];e++);var o=t-e;for(r=1;r<=o&&n[t-r]===i[l-r];r++);return Ni=i.slice(e,1<r?1-r:void 0)}function Fi(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function ki(){return!0}function xs(){return!1}function Ge(e){function n(t,r,i,l,o){this._reactName=t,this._targetInst=i,this.type=r,this.nativeEvent=l,this.target=o,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(t=e[u],this[u]=t?t(l):l[u]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?ki:xs,this.isPropagationStopped=xs,this}return ce(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var t=this.nativeEvent;t&&(t.preventDefault?t.preventDefault():typeof t.returnValue!="unknown"&&(t.returnValue=!1),this.isDefaultPrevented=ki)},stopPropagation:function(){var t=this.nativeEvent;t&&(t.stopPropagation?t.stopPropagation():typeof t.cancelBubble!="unknown"&&(t.cancelBubble=!0),this.isPropagationStopped=ki)},persist:function(){},isPersistent:ki}),n}var lr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},na=Ge(lr),ii=ce({},lr,{view:0,detail:0}),Fm=Ge(ii),Xl,Gl,mr,El=ce({},ii,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ta,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==mr&&(mr&&e.type==="mousemove"?(Xl=e.screenX-mr.screenX,Gl=e.screenY-mr.screenY):Gl=Xl=0,mr=e),Xl)},movementY:function(e){return"movementY"in e?e.movementY:Gl}}),ks=Ge(El),Mm=ce({},El,{dataTransfer:0}),Om=Ge(Mm),Bm=ce({},ii,{relatedTarget:0}),Zl=Ge(Bm),Um=ce({},lr,{animationName:0,elapsedTime:0,pseudoElement:0}),$m=Ge(Um),Hm=ce({},lr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Vm=Ge(Hm),Wm=ce({},lr,{data:0}),vs=Ge(Wm),Qm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},qm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ym={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Km(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=Ym[e])?!!n[e]:!1}function ta(){return Km}var Xm=ce({},ii,{key:function(e){if(e.key){var n=Qm[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=Fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?qm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ta,charCode:function(e){return e.type==="keypress"?Fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Gm=Ge(Xm),Zm=ce({},El,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ws=Ge(Zm),Jm=ce({},ii,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ta}),eg=Ge(Jm),ng=ce({},lr,{propertyName:0,elapsedTime:0,pseudoElement:0}),tg=Ge(ng),rg=ce({},El,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ig=Ge(rg),lg=[9,13,27,32],ra=Ln&&"CompositionEvent"in window,Pr=null;Ln&&"documentMode"in document&&(Pr=document.documentMode);var og=Ln&&"TextEvent"in window&&!Pr,ep=Ln&&(!ra||Pr&&8<Pr&&11>=Pr),Ss=String.fromCharCode(32),Cs=!1;function np(e,n){switch(e){case"keyup":return lg.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function tp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var At=!1;function ug(e,n){switch(e){case"compositionend":return tp(n);case"keypress":return n.which!==32?null:(Cs=!0,Ss);case"textInput":return e=n.data,e===Ss&&Cs?null:e;default:return null}}function ag(e,n){if(At)return e==="compositionend"||!ra&&np(e,n)?(e=Jf(),Ni=ea=Vn=null,At=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return ep&&n.locale!=="ko"?null:n.data;default:return null}}var sg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Es(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!sg[e.type]:n==="textarea"}function rp(e,n,t,r){Df(r),n=nl(n,"onChange"),0<n.length&&(t=new na("onChange","change",null,t,r),e.push({event:t,listeners:n}))}var Ir=null,Hr=null;function cg(e){hp(e,0)}function Tl(e){var n=Nt(e);if(Pf(n))return e}function fg(e,n){if(e==="change")return n}var ip=!1;if(Ln){var Jl;if(Ln){var eo="oninput"in document;if(!eo){var Ts=document.createElement("div");Ts.setAttribute("oninput","return;"),eo=typeof Ts.oninput=="function"}Jl=eo}else Jl=!1;ip=Jl&&(!document.documentMode||9<document.documentMode)}function zs(){Ir&&(Ir.detachEvent("onpropertychange",lp),Hr=Ir=null)}function lp(e){if(e.propertyName==="value"&&Tl(Hr)){var n=[];rp(n,Hr,e,Ku(e)),Mf(cg,n)}}function pg(e,n,t){e==="focusin"?(zs(),Ir=n,Hr=t,Ir.attachEvent("onpropertychange",lp)):e==="focusout"&&zs()}function dg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Tl(Hr)}function hg(e,n){if(e==="click")return Tl(n)}function mg(e,n){if(e==="input"||e==="change")return Tl(n)}function gg(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var mn=typeof Object.is=="function"?Object.is:gg;function Vr(e,n){if(mn(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var t=Object.keys(e),r=Object.keys(n);if(t.length!==r.length)return!1;for(r=0;r<t.length;r++){var i=t[r];if(!jo.call(n,i)||!mn(e[i],n[i]))return!1}return!0}function Ps(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Is(e,n){var t=Ps(e);e=0;for(var r;t;){if(t.nodeType===3){if(r=e+t.textContent.length,e<=n&&r>=n)return{node:t,offset:n-e};e=r}e:{for(;t;){if(t.nextSibling){t=t.nextSibling;break e}t=t.parentNode}t=void 0}t=Ps(t)}}function op(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?op(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function up(){for(var e=window,n=Yi();n instanceof e.HTMLIFrameElement;){try{var t=typeof n.contentWindow.location.href=="string"}catch{t=!1}if(t)e=n.contentWindow;else break;n=Yi(e.document)}return n}function ia(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}function yg(e){var n=up(),t=e.focusedElem,r=e.selectionRange;if(n!==t&&t&&t.ownerDocument&&op(t.ownerDocument.documentElement,t)){if(r!==null&&ia(t)){if(n=r.start,e=r.end,e===void 0&&(e=n),"selectionStart"in t)t.selectionStart=n,t.selectionEnd=Math.min(e,t.value.length);else if(e=(n=t.ownerDocument||document)&&n.defaultView||window,e.getSelection){e=e.getSelection();var i=t.textContent.length,l=Math.min(r.start,i);r=r.end===void 0?l:Math.min(r.end,i),!e.extend&&l>r&&(i=r,r=l,l=i),i=Is(t,l);var o=Is(t,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(n=n.createRange(),n.setStart(i.node,i.offset),e.removeAllRanges(),l>r?(e.addRange(n),e.extend(o.node,o.offset)):(n.setEnd(o.node,o.offset),e.addRange(n)))}}for(n=[],e=t;e=e.parentNode;)e.nodeType===1&&n.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof t.focus=="function"&&t.focus(),t=0;t<n.length;t++)e=n[t],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var xg=Ln&&"documentMode"in document&&11>=document.documentMode,Dt=null,eu=null,_r=null,nu=!1;function _s(e,n,t){var r=t.window===t?t.document:t.nodeType===9?t:t.ownerDocument;nu||Dt==null||Dt!==Yi(r)||(r=Dt,"selectionStart"in r&&ia(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),_r&&Vr(_r,r)||(_r=r,r=nl(eu,"onSelect"),0<r.length&&(n=new na("onSelect","select",null,n,t),e.push({event:n,listeners:r}),n.target=Dt)))}function vi(e,n){var t={};return t[e.toLowerCase()]=n.toLowerCase(),t["Webkit"+e]="webkit"+n,t["Moz"+e]="moz"+n,t}var jt={animationend:vi("Animation","AnimationEnd"),animationiteration:vi("Animation","AnimationIteration"),animationstart:vi("Animation","AnimationStart"),transitionend:vi("Transition","TransitionEnd")},no={},ap={};Ln&&(ap=document.createElement("div").style,"AnimationEvent"in window||(delete jt.animationend.animation,delete jt.animationiteration.animation,delete jt.animationstart.animation),"TransitionEvent"in window||delete jt.transitionend.transition);function zl(e){if(no[e])return no[e];if(!jt[e])return e;var n=jt[e],t;for(t in n)if(n.hasOwnProperty(t)&&t in ap)return no[e]=n[t];return e}var sp=zl("animationend"),cp=zl("animationiteration"),fp=zl("animationstart"),pp=zl("transitionend"),dp=new Map,bs="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function tt(e,n){dp.set(e,n),St(n,[e])}for(var to=0;to<bs.length;to++){var ro=bs[to],kg=ro.toLowerCase(),vg=ro[0].toUpperCase()+ro.slice(1);tt(kg,"on"+vg)}tt(sp,"onAnimationEnd");tt(cp,"onAnimationIteration");tt(fp,"onAnimationStart");tt("dblclick","onDoubleClick");tt("focusin","onFocus");tt("focusout","onBlur");tt(pp,"onTransitionEnd");Xt("onMouseEnter",["mouseout","mouseover"]);Xt("onMouseLeave",["mouseout","mouseover"]);Xt("onPointerEnter",["pointerout","pointerover"]);Xt("onPointerLeave",["pointerout","pointerover"]);St("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));St("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));St("onBeforeInput",["compositionend","keypress","textInput","paste"]);St("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));St("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));St("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Er="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),wg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Er));function Ls(e,n,t){var r=e.type||"unknown-event";e.currentTarget=t,km(r,n,void 0,e),e.currentTarget=null}function hp(e,n){n=(n&4)!==0;for(var t=0;t<e.length;t++){var r=e[t],i=r.event;r=r.listeners;e:{var l=void 0;if(n)for(var o=r.length-1;0<=o;o--){var u=r[o],a=u.instance,s=u.currentTarget;if(u=u.listener,a!==l&&i.isPropagationStopped())break e;Ls(i,u,s),l=a}else for(o=0;o<r.length;o++){if(u=r[o],a=u.instance,s=u.currentTarget,u=u.listener,a!==l&&i.isPropagationStopped())break e;Ls(i,u,s),l=a}}}if(Xi)throw e=Xo,Xi=!1,Xo=null,e}function ie(e,n){var t=n[ou];t===void 0&&(t=n[ou]=new Set);var r=e+"__bubble";t.has(r)||(mp(n,e,2,!1),t.add(r))}function io(e,n,t){var r=0;n&&(r|=4),mp(t,e,r,n)}var wi="_reactListening"+Math.random().toString(36).slice(2);function Wr(e){if(!e[wi]){e[wi]=!0,Sf.forEach(function(t){t!=="selectionchange"&&(wg.has(t)||io(t,!1,e),io(t,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[wi]||(n[wi]=!0,io("selectionchange",!1,n))}}function mp(e,n,t,r){switch(Zf(n)){case 1:var i=jm;break;case 4:i=Nm;break;default:i=Ju}t=i.bind(null,n,t,e),i=void 0,!Ko||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(n,t,{capture:!0,passive:i}):e.addEventListener(n,t,!0):i!==void 0?e.addEventListener(n,t,{passive:i}):e.addEventListener(n,t,!1)}function lo(e,n,t,r,i){var l=r;if(!(n&1)&&!(n&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var u=r.stateNode.containerInfo;if(u===i||u.nodeType===8&&u.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;o=o.return}for(;u!==null;){if(o=ft(u),o===null)return;if(a=o.tag,a===5||a===6){r=l=o;continue e}u=u.parentNode}}r=r.return}Mf(function(){var s=l,f=Ku(t),c=[];e:{var p=dp.get(e);if(p!==void 0){var d=na,g=e;switch(e){case"keypress":if(Fi(t)===0)break e;case"keydown":case"keyup":d=Gm;break;case"focusin":g="focus",d=Zl;break;case"focusout":g="blur",d=Zl;break;case"beforeblur":case"afterblur":d=Zl;break;case"click":if(t.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":d=ks;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":d=Om;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":d=eg;break;case sp:case cp:case fp:d=$m;break;case pp:d=tg;break;case"scroll":d=Fm;break;case"wheel":d=ig;break;case"copy":case"cut":case"paste":d=Vm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":d=ws}var v=(n&4)!==0,E=!v&&e==="scroll",h=v?p!==null?p+"Capture":null:p;v=[];for(var m=s,y;m!==null;){y=m;var T=y.stateNode;if(y.tag===5&&T!==null&&(y=T,h!==null&&(T=Or(m,h),T!=null&&v.push(Qr(m,T,y)))),E)break;m=m.return}0<v.length&&(p=new d(p,g,null,t,f),c.push({event:p,listeners:v}))}}if(!(n&7)){e:{if(p=e==="mouseover"||e==="pointerover",d=e==="mouseout"||e==="pointerout",p&&t!==qo&&(g=t.relatedTarget||t.fromElement)&&(ft(g)||g[Rn]))break e;if((d||p)&&(p=f.window===f?f:(p=f.ownerDocument)?p.defaultView||p.parentWindow:window,d?(g=t.relatedTarget||t.toElement,d=s,g=g?ft(g):null,g!==null&&(E=Ct(g),g!==E||g.tag!==5&&g.tag!==6)&&(g=null)):(d=null,g=s),d!==g)){if(v=ks,T="onMouseLeave",h="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(v=ws,T="onPointerLeave",h="onPointerEnter",m="pointer"),E=d==null?p:Nt(d),y=g==null?p:Nt(g),p=new v(T,m+"leave",d,t,f),p.target=E,p.relatedTarget=y,T=null,ft(f)===s&&(v=new v(h,m+"enter",g,t,f),v.target=y,v.relatedTarget=E,T=v),E=T,d&&g)n:{for(v=d,h=g,m=0,y=v;y;y=It(y))m++;for(y=0,T=h;T;T=It(T))y++;for(;0<m-y;)v=It(v),m--;for(;0<y-m;)h=It(h),y--;for(;m--;){if(v===h||h!==null&&v===h.alternate)break n;v=It(v),h=It(h)}v=null}else v=null;d!==null&&Rs(c,p,d,v,!1),g!==null&&E!==null&&Rs(c,E,g,v,!0)}}e:{if(p=s?Nt(s):window,d=p.nodeName&&p.nodeName.toLowerCase(),d==="select"||d==="input"&&p.type==="file")var P=fg;else if(Es(p))if(ip)P=mg;else{P=dg;var S=pg}else(d=p.nodeName)&&d.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(P=hg);if(P&&(P=P(e,s))){rp(c,P,t,f);break e}S&&S(e,p,s),e==="focusout"&&(S=p._wrapperState)&&S.controlled&&p.type==="number"&&$o(p,"number",p.value)}switch(S=s?Nt(s):window,e){case"focusin":(Es(S)||S.contentEditable==="true")&&(Dt=S,eu=s,_r=null);break;case"focusout":_r=eu=Dt=null;break;case"mousedown":nu=!0;break;case"contextmenu":case"mouseup":case"dragend":nu=!1,_s(c,t,f);break;case"selectionchange":if(xg)break;case"keydown":case"keyup":_s(c,t,f)}var _;if(ra)e:{switch(e){case"compositionstart":var L="onCompositionStart";break e;case"compositionend":L="onCompositionEnd";break e;case"compositionupdate":L="onCompositionUpdate";break e}L=void 0}else At?np(e,t)&&(L="onCompositionEnd"):e==="keydown"&&t.keyCode===229&&(L="onCompositionStart");L&&(ep&&t.locale!=="ko"&&(At||L!=="onCompositionStart"?L==="onCompositionEnd"&&At&&(_=Jf()):(Vn=f,ea="value"in Vn?Vn.value:Vn.textContent,At=!0)),S=nl(s,L),0<S.length&&(L=new vs(L,e,null,t,f),c.push({event:L,listeners:S}),_?L.data=_:(_=tp(t),_!==null&&(L.data=_)))),(_=og?ug(e,t):ag(e,t))&&(s=nl(s,"onBeforeInput"),0<s.length&&(f=new vs("onBeforeInput","beforeinput",null,t,f),c.push({event:f,listeners:s}),f.data=_))}hp(c,n)})}function Qr(e,n,t){return{instance:e,listener:n,currentTarget:t}}function nl(e,n){for(var t=n+"Capture",r=[];e!==null;){var i=e,l=i.stateNode;i.tag===5&&l!==null&&(i=l,l=Or(e,t),l!=null&&r.unshift(Qr(e,l,i)),l=Or(e,n),l!=null&&r.push(Qr(e,l,i))),e=e.return}return r}function It(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Rs(e,n,t,r,i){for(var l=n._reactName,o=[];t!==null&&t!==r;){var u=t,a=u.alternate,s=u.stateNode;if(a!==null&&a===r)break;u.tag===5&&s!==null&&(u=s,i?(a=Or(t,l),a!=null&&o.unshift(Qr(t,a,u))):i||(a=Or(t,l),a!=null&&o.push(Qr(t,a,u)))),t=t.return}o.length!==0&&e.push({event:n,listeners:o})}var Sg=/\r\n?/g,Cg=/\u0000|\uFFFD/g;function As(e){return(typeof e=="string"?e:""+e).replace(Sg,`
`).replace(Cg,"")}function Si(e,n,t){if(n=As(n),As(e)!==n&&t)throw Error(I(425))}function tl(){}var tu=null,ru=null;function iu(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var lu=typeof setTimeout=="function"?setTimeout:void 0,Eg=typeof clearTimeout=="function"?clearTimeout:void 0,Ds=typeof Promise=="function"?Promise:void 0,Tg=typeof queueMicrotask=="function"?queueMicrotask:typeof Ds<"u"?function(e){return Ds.resolve(null).then(e).catch(zg)}:lu;function zg(e){setTimeout(function(){throw e})}function oo(e,n){var t=n,r=0;do{var i=t.nextSibling;if(e.removeChild(t),i&&i.nodeType===8)if(t=i.data,t==="/$"){if(r===0){e.removeChild(i),$r(n);return}r--}else t!=="$"&&t!=="$?"&&t!=="$!"||r++;t=i}while(t);$r(n)}function Kn(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?")break;if(n==="/$")return null}}return e}function js(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var t=e.data;if(t==="$"||t==="$!"||t==="$?"){if(n===0)return e;n--}else t==="/$"&&n++}e=e.previousSibling}return null}var or=Math.random().toString(36).slice(2),vn="__reactFiber$"+or,qr="__reactProps$"+or,Rn="__reactContainer$"+or,ou="__reactEvents$"+or,Pg="__reactListeners$"+or,Ig="__reactHandles$"+or;function ft(e){var n=e[vn];if(n)return n;for(var t=e.parentNode;t;){if(n=t[Rn]||t[vn]){if(t=n.alternate,n.child!==null||t!==null&&t.child!==null)for(e=js(e);e!==null;){if(t=e[vn])return t;e=js(e)}return n}e=t,t=e.parentNode}return null}function li(e){return e=e[vn]||e[Rn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Nt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(I(33))}function Pl(e){return e[qr]||null}var uu=[],Ft=-1;function rt(e){return{current:e}}function oe(e){0>Ft||(e.current=uu[Ft],uu[Ft]=null,Ft--)}function te(e,n){Ft++,uu[Ft]=e.current,e.current=n}var nt={},_e=rt(nt),Oe=rt(!1),gt=nt;function Gt(e,n){var t=e.type.contextTypes;if(!t)return nt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===n)return r.__reactInternalMemoizedMaskedChildContext;var i={},l;for(l in t)i[l]=n[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=n,e.__reactInternalMemoizedMaskedChildContext=i),i}function Be(e){return e=e.childContextTypes,e!=null}function rl(){oe(Oe),oe(_e)}function Ns(e,n,t){if(_e.current!==nt)throw Error(I(168));te(_e,n),te(Oe,t)}function gp(e,n,t){var r=e.stateNode;if(n=n.childContextTypes,typeof r.getChildContext!="function")return t;r=r.getChildContext();for(var i in r)if(!(i in n))throw Error(I(108,pm(e)||"Unknown",i));return ce({},t,r)}function il(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||nt,gt=_e.current,te(_e,e),te(Oe,Oe.current),!0}function Fs(e,n,t){var r=e.stateNode;if(!r)throw Error(I(169));t?(e=gp(e,n,gt),r.__reactInternalMemoizedMergedChildContext=e,oe(Oe),oe(_e),te(_e,e)):oe(Oe),te(Oe,t)}var Pn=null,Il=!1,uo=!1;function yp(e){Pn===null?Pn=[e]:Pn.push(e)}function _g(e){Il=!0,yp(e)}function it(){if(!uo&&Pn!==null){uo=!0;var e=0,n=X;try{var t=Pn;for(X=1;e<t.length;e++){var r=t[e];do r=r(!0);while(r!==null)}Pn=null,Il=!1}catch(i){throw Pn!==null&&(Pn=Pn.slice(e+1)),$f(Xu,it),i}finally{X=n,uo=!1}}return null}var Mt=[],Ot=0,ll=null,ol=0,Ze=[],Je=0,yt=null,In=1,_n="";function at(e,n){Mt[Ot++]=ol,Mt[Ot++]=ll,ll=e,ol=n}function xp(e,n,t){Ze[Je++]=In,Ze[Je++]=_n,Ze[Je++]=yt,yt=e;var r=In;e=_n;var i=32-pn(r)-1;r&=~(1<<i),t+=1;var l=32-pn(n)+i;if(30<l){var o=i-i%5;l=(r&(1<<o)-1).toString(32),r>>=o,i-=o,In=1<<32-pn(n)+i|t<<i|r,_n=l+e}else In=1<<l|t<<i|r,_n=e}function la(e){e.return!==null&&(at(e,1),xp(e,1,0))}function oa(e){for(;e===ll;)ll=Mt[--Ot],Mt[Ot]=null,ol=Mt[--Ot],Mt[Ot]=null;for(;e===yt;)yt=Ze[--Je],Ze[Je]=null,_n=Ze[--Je],Ze[Je]=null,In=Ze[--Je],Ze[Je]=null}var qe=null,Qe=null,ue=!1,fn=null;function kp(e,n){var t=nn(5,null,null,0);t.elementType="DELETED",t.stateNode=n,t.return=e,n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)}function Ms(e,n){switch(e.tag){case 5:var t=e.type;return n=n.nodeType!==1||t.toLowerCase()!==n.nodeName.toLowerCase()?null:n,n!==null?(e.stateNode=n,qe=e,Qe=Kn(n.firstChild),!0):!1;case 6:return n=e.pendingProps===""||n.nodeType!==3?null:n,n!==null?(e.stateNode=n,qe=e,Qe=null,!0):!1;case 13:return n=n.nodeType!==8?null:n,n!==null?(t=yt!==null?{id:In,overflow:_n}:null,e.memoizedState={dehydrated:n,treeContext:t,retryLane:1073741824},t=nn(18,null,null,0),t.stateNode=n,t.return=e,e.child=t,qe=e,Qe=null,!0):!1;default:return!1}}function au(e){return(e.mode&1)!==0&&(e.flags&128)===0}function su(e){if(ue){var n=Qe;if(n){var t=n;if(!Ms(e,n)){if(au(e))throw Error(I(418));n=Kn(t.nextSibling);var r=qe;n&&Ms(e,n)?kp(r,t):(e.flags=e.flags&-4097|2,ue=!1,qe=e)}}else{if(au(e))throw Error(I(418));e.flags=e.flags&-4097|2,ue=!1,qe=e}}}function Os(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;qe=e}function Ci(e){if(e!==qe)return!1;if(!ue)return Os(e),ue=!0,!1;var n;if((n=e.tag!==3)&&!(n=e.tag!==5)&&(n=e.type,n=n!=="head"&&n!=="body"&&!iu(e.type,e.memoizedProps)),n&&(n=Qe)){if(au(e))throw vp(),Error(I(418));for(;n;)kp(e,n),n=Kn(n.nextSibling)}if(Os(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(I(317));e:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8){var t=e.data;if(t==="/$"){if(n===0){Qe=Kn(e.nextSibling);break e}n--}else t!=="$"&&t!=="$!"&&t!=="$?"||n++}e=e.nextSibling}Qe=null}}else Qe=qe?Kn(e.stateNode.nextSibling):null;return!0}function vp(){for(var e=Qe;e;)e=Kn(e.nextSibling)}function Zt(){Qe=qe=null,ue=!1}function ua(e){fn===null?fn=[e]:fn.push(e)}var bg=jn.ReactCurrentBatchConfig;function sn(e,n){if(e&&e.defaultProps){n=ce({},n),e=e.defaultProps;for(var t in e)n[t]===void 0&&(n[t]=e[t]);return n}return n}var ul=rt(null),al=null,Bt=null,aa=null;function sa(){aa=Bt=al=null}function ca(e){var n=ul.current;oe(ul),e._currentValue=n}function cu(e,n,t){for(;e!==null;){var r=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,r!==null&&(r.childLanes|=n)):r!==null&&(r.childLanes&n)!==n&&(r.childLanes|=n),e===t)break;e=e.return}}function qt(e,n){al=e,aa=Bt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&n&&(Me=!0),e.firstContext=null)}function rn(e){var n=e._currentValue;if(aa!==e)if(e={context:e,memoizedValue:n,next:null},Bt===null){if(al===null)throw Error(I(308));Bt=e,al.dependencies={lanes:0,firstContext:e}}else Bt=Bt.next=e;return n}var pt=null;function fa(e){pt===null?pt=[e]:pt.push(e)}function wp(e,n,t,r){var i=n.interleaved;return i===null?(t.next=t,fa(n)):(t.next=i.next,i.next=t),n.interleaved=t,An(e,r)}function An(e,n){e.lanes|=n;var t=e.alternate;for(t!==null&&(t.lanes|=n),t=e,e=e.return;e!==null;)e.childLanes|=n,t=e.alternate,t!==null&&(t.childLanes|=n),t=e,e=e.return;return t.tag===3?t.stateNode:null}var Un=!1;function pa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Sp(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function bn(e,n){return{eventTime:e,lane:n,tag:0,payload:null,callback:null,next:null}}function Xn(e,n,t){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,K&2){var i=r.pending;return i===null?n.next=n:(n.next=i.next,i.next=n),r.pending=n,An(e,t)}return i=r.interleaved,i===null?(n.next=n,fa(r)):(n.next=i.next,i.next=n),r.interleaved=n,An(e,t)}function Mi(e,n,t){if(n=n.updateQueue,n!==null&&(n=n.shared,(t&4194240)!==0)){var r=n.lanes;r&=e.pendingLanes,t|=r,n.lanes=t,Gu(e,t)}}function Bs(e,n){var t=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,t===r)){var i=null,l=null;if(t=t.firstBaseUpdate,t!==null){do{var o={eventTime:t.eventTime,lane:t.lane,tag:t.tag,payload:t.payload,callback:t.callback,next:null};l===null?i=l=o:l=l.next=o,t=t.next}while(t!==null);l===null?i=l=n:l=l.next=n}else i=l=n;t={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=t;return}e=t.lastBaseUpdate,e===null?t.firstBaseUpdate=n:e.next=n,t.lastBaseUpdate=n}function sl(e,n,t,r){var i=e.updateQueue;Un=!1;var l=i.firstBaseUpdate,o=i.lastBaseUpdate,u=i.shared.pending;if(u!==null){i.shared.pending=null;var a=u,s=a.next;a.next=null,o===null?l=s:o.next=s,o=a;var f=e.alternate;f!==null&&(f=f.updateQueue,u=f.lastBaseUpdate,u!==o&&(u===null?f.firstBaseUpdate=s:u.next=s,f.lastBaseUpdate=a))}if(l!==null){var c=i.baseState;o=0,f=s=a=null,u=l;do{var p=u.lane,d=u.eventTime;if((r&p)===p){f!==null&&(f=f.next={eventTime:d,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var g=e,v=u;switch(p=n,d=t,v.tag){case 1:if(g=v.payload,typeof g=="function"){c=g.call(d,c,p);break e}c=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=v.payload,p=typeof g=="function"?g.call(d,c,p):g,p==null)break e;c=ce({},c,p);break e;case 2:Un=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[u]:p.push(u))}else d={eventTime:d,lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},f===null?(s=f=d,a=c):f=f.next=d,o|=p;if(u=u.next,u===null){if(u=i.shared.pending,u===null)break;p=u,u=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(1);if(f===null&&(a=c),i.baseState=a,i.firstBaseUpdate=s,i.lastBaseUpdate=f,n=i.shared.interleaved,n!==null){i=n;do o|=i.lane,i=i.next;while(i!==n)}else l===null&&(i.shared.lanes=0);kt|=o,e.lanes=o,e.memoizedState=c}}function Us(e,n,t){if(e=n.effects,n.effects=null,e!==null)for(n=0;n<e.length;n++){var r=e[n],i=r.callback;if(i!==null){if(r.callback=null,r=t,typeof i!="function")throw Error(I(191,i));i.call(r)}}}var Cp=new wf.Component().refs;function fu(e,n,t,r){n=e.memoizedState,t=t(r,n),t=t==null?n:ce({},n,t),e.memoizedState=t,e.lanes===0&&(e.updateQueue.baseState=t)}var _l={isMounted:function(e){return(e=e._reactInternals)?Ct(e)===e:!1},enqueueSetState:function(e,n,t){e=e._reactInternals;var r=Ae(),i=Zn(e),l=bn(r,i);l.payload=n,t!=null&&(l.callback=t),n=Xn(e,l,i),n!==null&&(dn(n,e,i,r),Mi(n,e,i))},enqueueReplaceState:function(e,n,t){e=e._reactInternals;var r=Ae(),i=Zn(e),l=bn(r,i);l.tag=1,l.payload=n,t!=null&&(l.callback=t),n=Xn(e,l,i),n!==null&&(dn(n,e,i,r),Mi(n,e,i))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var t=Ae(),r=Zn(e),i=bn(t,r);i.tag=2,n!=null&&(i.callback=n),n=Xn(e,i,r),n!==null&&(dn(n,e,r,t),Mi(n,e,r))}};function $s(e,n,t,r,i,l,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,o):n.prototype&&n.prototype.isPureReactComponent?!Vr(t,r)||!Vr(i,l):!0}function Ep(e,n,t){var r=!1,i=nt,l=n.contextType;return typeof l=="object"&&l!==null?l=rn(l):(i=Be(n)?gt:_e.current,r=n.contextTypes,l=(r=r!=null)?Gt(e,i):nt),n=new n(t,l),e.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=_l,e.stateNode=n,n._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=l),n}function Hs(e,n,t,r){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(t,r),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(t,r),n.state!==e&&_l.enqueueReplaceState(n,n.state,null)}function pu(e,n,t,r){var i=e.stateNode;i.props=t,i.state=e.memoizedState,i.refs=Cp,pa(e);var l=n.contextType;typeof l=="object"&&l!==null?i.context=rn(l):(l=Be(n)?gt:_e.current,i.context=Gt(e,l)),i.state=e.memoizedState,l=n.getDerivedStateFromProps,typeof l=="function"&&(fu(e,n,l,t),i.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(n=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),n!==i.state&&_l.enqueueReplaceState(i,i.state,null),sl(e,t,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function gr(e,n,t){if(e=t.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(t._owner){if(t=t._owner,t){if(t.tag!==1)throw Error(I(309));var r=t.stateNode}if(!r)throw Error(I(147,e));var i=r,l=""+e;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===l?n.ref:(n=function(o){var u=i.refs;u===Cp&&(u=i.refs={}),o===null?delete u[l]:u[l]=o},n._stringRef=l,n)}if(typeof e!="string")throw Error(I(284));if(!t._owner)throw Error(I(290,e))}return e}function Ei(e,n){throw e=Object.prototype.toString.call(n),Error(I(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function Vs(e){var n=e._init;return n(e._payload)}function Tp(e){function n(h,m){if(e){var y=h.deletions;y===null?(h.deletions=[m],h.flags|=16):y.push(m)}}function t(h,m){if(!e)return null;for(;m!==null;)n(h,m),m=m.sibling;return null}function r(h,m){for(h=new Map;m!==null;)m.key!==null?h.set(m.key,m):h.set(m.index,m),m=m.sibling;return h}function i(h,m){return h=Jn(h,m),h.index=0,h.sibling=null,h}function l(h,m,y){return h.index=y,e?(y=h.alternate,y!==null?(y=y.index,y<m?(h.flags|=2,m):y):(h.flags|=2,m)):(h.flags|=1048576,m)}function o(h){return e&&h.alternate===null&&(h.flags|=2),h}function u(h,m,y,T){return m===null||m.tag!==6?(m=mo(y,h.mode,T),m.return=h,m):(m=i(m,y),m.return=h,m)}function a(h,m,y,T){var P=y.type;return P===Rt?f(h,m,y.props.children,T,y.key):m!==null&&(m.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Bn&&Vs(P)===m.type)?(T=i(m,y.props),T.ref=gr(h,m,y),T.return=h,T):(T=Vi(y.type,y.key,y.props,null,h.mode,T),T.ref=gr(h,m,y),T.return=h,T)}function s(h,m,y,T){return m===null||m.tag!==4||m.stateNode.containerInfo!==y.containerInfo||m.stateNode.implementation!==y.implementation?(m=go(y,h.mode,T),m.return=h,m):(m=i(m,y.children||[]),m.return=h,m)}function f(h,m,y,T,P){return m===null||m.tag!==7?(m=mt(y,h.mode,T,P),m.return=h,m):(m=i(m,y),m.return=h,m)}function c(h,m,y){if(typeof m=="string"&&m!==""||typeof m=="number")return m=mo(""+m,h.mode,y),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case di:return y=Vi(m.type,m.key,m.props,null,h.mode,y),y.ref=gr(h,null,m),y.return=h,y;case Lt:return m=go(m,h.mode,y),m.return=h,m;case Bn:var T=m._init;return c(h,T(m._payload),y)}if(Sr(m)||fr(m))return m=mt(m,h.mode,y,null),m.return=h,m;Ei(h,m)}return null}function p(h,m,y,T){var P=m!==null?m.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return P!==null?null:u(h,m,""+y,T);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case di:return y.key===P?a(h,m,y,T):null;case Lt:return y.key===P?s(h,m,y,T):null;case Bn:return P=y._init,p(h,m,P(y._payload),T)}if(Sr(y)||fr(y))return P!==null?null:f(h,m,y,T,null);Ei(h,y)}return null}function d(h,m,y,T,P){if(typeof T=="string"&&T!==""||typeof T=="number")return h=h.get(y)||null,u(m,h,""+T,P);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case di:return h=h.get(T.key===null?y:T.key)||null,a(m,h,T,P);case Lt:return h=h.get(T.key===null?y:T.key)||null,s(m,h,T,P);case Bn:var S=T._init;return d(h,m,y,S(T._payload),P)}if(Sr(T)||fr(T))return h=h.get(y)||null,f(m,h,T,P,null);Ei(m,T)}return null}function g(h,m,y,T){for(var P=null,S=null,_=m,L=m=0,M=null;_!==null&&L<y.length;L++){_.index>L?(M=_,_=null):M=_.sibling;var C=p(h,_,y[L],T);if(C===null){_===null&&(_=M);break}e&&_&&C.alternate===null&&n(h,_),m=l(C,m,L),S===null?P=C:S.sibling=C,S=C,_=M}if(L===y.length)return t(h,_),ue&&at(h,L),P;if(_===null){for(;L<y.length;L++)_=c(h,y[L],T),_!==null&&(m=l(_,m,L),S===null?P=_:S.sibling=_,S=_);return ue&&at(h,L),P}for(_=r(h,_);L<y.length;L++)M=d(_,h,L,y[L],T),M!==null&&(e&&M.alternate!==null&&_.delete(M.key===null?L:M.key),m=l(M,m,L),S===null?P=M:S.sibling=M,S=M);return e&&_.forEach(function(D){return n(h,D)}),ue&&at(h,L),P}function v(h,m,y,T){var P=fr(y);if(typeof P!="function")throw Error(I(150));if(y=P.call(y),y==null)throw Error(I(151));for(var S=P=null,_=m,L=m=0,M=null,C=y.next();_!==null&&!C.done;L++,C=y.next()){_.index>L?(M=_,_=null):M=_.sibling;var D=p(h,_,C.value,T);if(D===null){_===null&&(_=M);break}e&&_&&D.alternate===null&&n(h,_),m=l(D,m,L),S===null?P=D:S.sibling=D,S=D,_=M}if(C.done)return t(h,_),ue&&at(h,L),P;if(_===null){for(;!C.done;L++,C=y.next())C=c(h,C.value,T),C!==null&&(m=l(C,m,L),S===null?P=C:S.sibling=C,S=C);return ue&&at(h,L),P}for(_=r(h,_);!C.done;L++,C=y.next())C=d(_,h,L,C.value,T),C!==null&&(e&&C.alternate!==null&&_.delete(C.key===null?L:C.key),m=l(C,m,L),S===null?P=C:S.sibling=C,S=C);return e&&_.forEach(function(N){return n(h,N)}),ue&&at(h,L),P}function E(h,m,y,T){if(typeof y=="object"&&y!==null&&y.type===Rt&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case di:e:{for(var P=y.key,S=m;S!==null;){if(S.key===P){if(P=y.type,P===Rt){if(S.tag===7){t(h,S.sibling),m=i(S,y.props.children),m.return=h,h=m;break e}}else if(S.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Bn&&Vs(P)===S.type){t(h,S.sibling),m=i(S,y.props),m.ref=gr(h,S,y),m.return=h,h=m;break e}t(h,S);break}else n(h,S);S=S.sibling}y.type===Rt?(m=mt(y.props.children,h.mode,T,y.key),m.return=h,h=m):(T=Vi(y.type,y.key,y.props,null,h.mode,T),T.ref=gr(h,m,y),T.return=h,h=T)}return o(h);case Lt:e:{for(S=y.key;m!==null;){if(m.key===S)if(m.tag===4&&m.stateNode.containerInfo===y.containerInfo&&m.stateNode.implementation===y.implementation){t(h,m.sibling),m=i(m,y.children||[]),m.return=h,h=m;break e}else{t(h,m);break}else n(h,m);m=m.sibling}m=go(y,h.mode,T),m.return=h,h=m}return o(h);case Bn:return S=y._init,E(h,m,S(y._payload),T)}if(Sr(y))return g(h,m,y,T);if(fr(y))return v(h,m,y,T);Ei(h,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,m!==null&&m.tag===6?(t(h,m.sibling),m=i(m,y),m.return=h,h=m):(t(h,m),m=mo(y,h.mode,T),m.return=h,h=m),o(h)):t(h,m)}return E}var Jt=Tp(!0),zp=Tp(!1),oi={},Sn=rt(oi),Yr=rt(oi),Kr=rt(oi);function dt(e){if(e===oi)throw Error(I(174));return e}function da(e,n){switch(te(Kr,n),te(Yr,e),te(Sn,oi),e=n.nodeType,e){case 9:case 11:n=(n=n.documentElement)?n.namespaceURI:Vo(null,"");break;default:e=e===8?n.parentNode:n,n=e.namespaceURI||null,e=e.tagName,n=Vo(n,e)}oe(Sn),te(Sn,n)}function er(){oe(Sn),oe(Yr),oe(Kr)}function Pp(e){dt(Kr.current);var n=dt(Sn.current),t=Vo(n,e.type);n!==t&&(te(Yr,e),te(Sn,t))}function ha(e){Yr.current===e&&(oe(Sn),oe(Yr))}var ae=rt(0);function cl(e){for(var n=e;n!==null;){if(n.tag===13){var t=n.memoizedState;if(t!==null&&(t=t.dehydrated,t===null||t.data==="$?"||t.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if(n.flags&128)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var ao=[];function ma(){for(var e=0;e<ao.length;e++)ao[e]._workInProgressVersionPrimary=null;ao.length=0}var Oi=jn.ReactCurrentDispatcher,so=jn.ReactCurrentBatchConfig,xt=0,se=null,xe=null,ve=null,fl=!1,br=!1,Xr=0,Lg=0;function Te(){throw Error(I(321))}function ga(e,n){if(n===null)return!1;for(var t=0;t<n.length&&t<e.length;t++)if(!mn(e[t],n[t]))return!1;return!0}function ya(e,n,t,r,i,l){if(xt=l,se=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,Oi.current=e===null||e.memoizedState===null?jg:Ng,e=t(r,i),br){l=0;do{if(br=!1,Xr=0,25<=l)throw Error(I(301));l+=1,ve=xe=null,n.updateQueue=null,Oi.current=Fg,e=t(r,i)}while(br)}if(Oi.current=pl,n=xe!==null&&xe.next!==null,xt=0,ve=xe=se=null,fl=!1,n)throw Error(I(300));return e}function xa(){var e=Xr!==0;return Xr=0,e}function xn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ve===null?se.memoizedState=ve=e:ve=ve.next=e,ve}function ln(){if(xe===null){var e=se.alternate;e=e!==null?e.memoizedState:null}else e=xe.next;var n=ve===null?se.memoizedState:ve.next;if(n!==null)ve=n,xe=e;else{if(e===null)throw Error(I(310));xe=e,e={memoizedState:xe.memoizedState,baseState:xe.baseState,baseQueue:xe.baseQueue,queue:xe.queue,next:null},ve===null?se.memoizedState=ve=e:ve=ve.next=e}return ve}function Gr(e,n){return typeof n=="function"?n(e):n}function co(e){var n=ln(),t=n.queue;if(t===null)throw Error(I(311));t.lastRenderedReducer=e;var r=xe,i=r.baseQueue,l=t.pending;if(l!==null){if(i!==null){var o=i.next;i.next=l.next,l.next=o}r.baseQueue=i=l,t.pending=null}if(i!==null){l=i.next,r=r.baseState;var u=o=null,a=null,s=l;do{var f=s.lane;if((xt&f)===f)a!==null&&(a=a.next={lane:0,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null}),r=s.hasEagerState?s.eagerState:e(r,s.action);else{var c={lane:f,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null};a===null?(u=a=c,o=r):a=a.next=c,se.lanes|=f,kt|=f}s=s.next}while(s!==null&&s!==l);a===null?o=r:a.next=u,mn(r,n.memoizedState)||(Me=!0),n.memoizedState=r,n.baseState=o,n.baseQueue=a,t.lastRenderedState=r}if(e=t.interleaved,e!==null){i=e;do l=i.lane,se.lanes|=l,kt|=l,i=i.next;while(i!==e)}else i===null&&(t.lanes=0);return[n.memoizedState,t.dispatch]}function fo(e){var n=ln(),t=n.queue;if(t===null)throw Error(I(311));t.lastRenderedReducer=e;var r=t.dispatch,i=t.pending,l=n.memoizedState;if(i!==null){t.pending=null;var o=i=i.next;do l=e(l,o.action),o=o.next;while(o!==i);mn(l,n.memoizedState)||(Me=!0),n.memoizedState=l,n.baseQueue===null&&(n.baseState=l),t.lastRenderedState=l}return[l,r]}function Ip(){}function _p(e,n){var t=se,r=ln(),i=n(),l=!mn(r.memoizedState,i);if(l&&(r.memoizedState=i,Me=!0),r=r.queue,ka(Rp.bind(null,t,r,e),[e]),r.getSnapshot!==n||l||ve!==null&&ve.memoizedState.tag&1){if(t.flags|=2048,Zr(9,Lp.bind(null,t,r,i,n),void 0,null),we===null)throw Error(I(349));xt&30||bp(t,n,i)}return i}function bp(e,n,t){e.flags|=16384,e={getSnapshot:n,value:t},n=se.updateQueue,n===null?(n={lastEffect:null,stores:null},se.updateQueue=n,n.stores=[e]):(t=n.stores,t===null?n.stores=[e]:t.push(e))}function Lp(e,n,t,r){n.value=t,n.getSnapshot=r,Ap(n)&&Dp(e)}function Rp(e,n,t){return t(function(){Ap(n)&&Dp(e)})}function Ap(e){var n=e.getSnapshot;e=e.value;try{var t=n();return!mn(e,t)}catch{return!0}}function Dp(e){var n=An(e,1);n!==null&&dn(n,e,1,-1)}function Ws(e){var n=xn();return typeof e=="function"&&(e=e()),n.memoizedState=n.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Gr,lastRenderedState:e},n.queue=e,e=e.dispatch=Dg.bind(null,se,e),[n.memoizedState,e]}function Zr(e,n,t,r){return e={tag:e,create:n,destroy:t,deps:r,next:null},n=se.updateQueue,n===null?(n={lastEffect:null,stores:null},se.updateQueue=n,n.lastEffect=e.next=e):(t=n.lastEffect,t===null?n.lastEffect=e.next=e:(r=t.next,t.next=e,e.next=r,n.lastEffect=e)),e}function jp(){return ln().memoizedState}function Bi(e,n,t,r){var i=xn();se.flags|=e,i.memoizedState=Zr(1|n,t,void 0,r===void 0?null:r)}function bl(e,n,t,r){var i=ln();r=r===void 0?null:r;var l=void 0;if(xe!==null){var o=xe.memoizedState;if(l=o.destroy,r!==null&&ga(r,o.deps)){i.memoizedState=Zr(n,t,l,r);return}}se.flags|=e,i.memoizedState=Zr(1|n,t,l,r)}function Qs(e,n){return Bi(8390656,8,e,n)}function ka(e,n){return bl(2048,8,e,n)}function Np(e,n){return bl(4,2,e,n)}function Fp(e,n){return bl(4,4,e,n)}function Mp(e,n){if(typeof n=="function")return e=e(),n(e),function(){n(null)};if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function Op(e,n,t){return t=t!=null?t.concat([e]):null,bl(4,4,Mp.bind(null,n,e),t)}function va(){}function Bp(e,n){var t=ln();n=n===void 0?null:n;var r=t.memoizedState;return r!==null&&n!==null&&ga(n,r[1])?r[0]:(t.memoizedState=[e,n],e)}function Up(e,n){var t=ln();n=n===void 0?null:n;var r=t.memoizedState;return r!==null&&n!==null&&ga(n,r[1])?r[0]:(e=e(),t.memoizedState=[e,n],e)}function $p(e,n,t){return xt&21?(mn(t,n)||(t=Wf(),se.lanes|=t,kt|=t,e.baseState=!0),n):(e.baseState&&(e.baseState=!1,Me=!0),e.memoizedState=t)}function Rg(e,n){var t=X;X=t!==0&&4>t?t:4,e(!0);var r=so.transition;so.transition={};try{e(!1),n()}finally{X=t,so.transition=r}}function Hp(){return ln().memoizedState}function Ag(e,n,t){var r=Zn(e);if(t={lane:r,action:t,hasEagerState:!1,eagerState:null,next:null},Vp(e))Wp(n,t);else if(t=wp(e,n,t,r),t!==null){var i=Ae();dn(t,e,r,i),Qp(t,n,r)}}function Dg(e,n,t){var r=Zn(e),i={lane:r,action:t,hasEagerState:!1,eagerState:null,next:null};if(Vp(e))Wp(n,i);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=n.lastRenderedReducer,l!==null))try{var o=n.lastRenderedState,u=l(o,t);if(i.hasEagerState=!0,i.eagerState=u,mn(u,o)){var a=n.interleaved;a===null?(i.next=i,fa(n)):(i.next=a.next,a.next=i),n.interleaved=i;return}}catch{}finally{}t=wp(e,n,i,r),t!==null&&(i=Ae(),dn(t,e,r,i),Qp(t,n,r))}}function Vp(e){var n=e.alternate;return e===se||n!==null&&n===se}function Wp(e,n){br=fl=!0;var t=e.pending;t===null?n.next=n:(n.next=t.next,t.next=n),e.pending=n}function Qp(e,n,t){if(t&4194240){var r=n.lanes;r&=e.pendingLanes,t|=r,n.lanes=t,Gu(e,t)}}var pl={readContext:rn,useCallback:Te,useContext:Te,useEffect:Te,useImperativeHandle:Te,useInsertionEffect:Te,useLayoutEffect:Te,useMemo:Te,useReducer:Te,useRef:Te,useState:Te,useDebugValue:Te,useDeferredValue:Te,useTransition:Te,useMutableSource:Te,useSyncExternalStore:Te,useId:Te,unstable_isNewReconciler:!1},jg={readContext:rn,useCallback:function(e,n){return xn().memoizedState=[e,n===void 0?null:n],e},useContext:rn,useEffect:Qs,useImperativeHandle:function(e,n,t){return t=t!=null?t.concat([e]):null,Bi(4194308,4,Mp.bind(null,n,e),t)},useLayoutEffect:function(e,n){return Bi(4194308,4,e,n)},useInsertionEffect:function(e,n){return Bi(4,2,e,n)},useMemo:function(e,n){var t=xn();return n=n===void 0?null:n,e=e(),t.memoizedState=[e,n],e},useReducer:function(e,n,t){var r=xn();return n=t!==void 0?t(n):n,r.memoizedState=r.baseState=n,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},r.queue=e,e=e.dispatch=Ag.bind(null,se,e),[r.memoizedState,e]},useRef:function(e){var n=xn();return e={current:e},n.memoizedState=e},useState:Ws,useDebugValue:va,useDeferredValue:function(e){return xn().memoizedState=e},useTransition:function(){var e=Ws(!1),n=e[0];return e=Rg.bind(null,e[1]),xn().memoizedState=e,[n,e]},useMutableSource:function(){},useSyncExternalStore:function(e,n,t){var r=se,i=xn();if(ue){if(t===void 0)throw Error(I(407));t=t()}else{if(t=n(),we===null)throw Error(I(349));xt&30||bp(r,n,t)}i.memoizedState=t;var l={value:t,getSnapshot:n};return i.queue=l,Qs(Rp.bind(null,r,l,e),[e]),r.flags|=2048,Zr(9,Lp.bind(null,r,l,t,n),void 0,null),t},useId:function(){var e=xn(),n=we.identifierPrefix;if(ue){var t=_n,r=In;t=(r&~(1<<32-pn(r)-1)).toString(32)+t,n=":"+n+"R"+t,t=Xr++,0<t&&(n+="H"+t.toString(32)),n+=":"}else t=Lg++,n=":"+n+"r"+t.toString(32)+":";return e.memoizedState=n},unstable_isNewReconciler:!1},Ng={readContext:rn,useCallback:Bp,useContext:rn,useEffect:ka,useImperativeHandle:Op,useInsertionEffect:Np,useLayoutEffect:Fp,useMemo:Up,useReducer:co,useRef:jp,useState:function(){return co(Gr)},useDebugValue:va,useDeferredValue:function(e){var n=ln();return $p(n,xe.memoizedState,e)},useTransition:function(){var e=co(Gr)[0],n=ln().memoizedState;return[e,n]},useMutableSource:Ip,useSyncExternalStore:_p,useId:Hp,unstable_isNewReconciler:!1},Fg={readContext:rn,useCallback:Bp,useContext:rn,useEffect:ka,useImperativeHandle:Op,useInsertionEffect:Np,useLayoutEffect:Fp,useMemo:Up,useReducer:fo,useRef:jp,useState:function(){return fo(Gr)},useDebugValue:va,useDeferredValue:function(e){var n=ln();return xe===null?n.memoizedState=e:$p(n,xe.memoizedState,e)},useTransition:function(){var e=fo(Gr)[0],n=ln().memoizedState;return[e,n]},useMutableSource:Ip,useSyncExternalStore:_p,useId:Hp,unstable_isNewReconciler:!1};function nr(e,n){try{var t="",r=n;do t+=fm(r),r=r.return;while(r);var i=t}catch(l){i=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:n,stack:i,digest:null}}function po(e,n,t){return{value:e,source:null,stack:t??null,digest:n??null}}function du(e,n){try{console.error(n.value)}catch(t){setTimeout(function(){throw t})}}var Mg=typeof WeakMap=="function"?WeakMap:Map;function qp(e,n,t){t=bn(-1,t),t.tag=3,t.payload={element:null};var r=n.value;return t.callback=function(){hl||(hl=!0,Cu=r),du(e,n)},t}function Yp(e,n,t){t=bn(-1,t),t.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=n.value;t.payload=function(){return r(i)},t.callback=function(){du(e,n)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(t.callback=function(){du(e,n),typeof r!="function"&&(Gn===null?Gn=new Set([this]):Gn.add(this));var o=n.stack;this.componentDidCatch(n.value,{componentStack:o!==null?o:""})}),t}function qs(e,n,t){var r=e.pingCache;if(r===null){r=e.pingCache=new Mg;var i=new Set;r.set(n,i)}else i=r.get(n),i===void 0&&(i=new Set,r.set(n,i));i.has(t)||(i.add(t),e=Zg.bind(null,e,n,t),n.then(e,e))}function Ys(e){do{var n;if((n=e.tag===13)&&(n=e.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return e;e=e.return}while(e!==null);return null}function Ks(e,n,t,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===n?e.flags|=65536:(e.flags|=128,t.flags|=131072,t.flags&=-52805,t.tag===1&&(t.alternate===null?t.tag=17:(n=bn(-1,1),n.tag=2,Xn(t,n,1))),t.lanes|=1),e)}var Og=jn.ReactCurrentOwner,Me=!1;function Le(e,n,t,r){n.child=e===null?zp(n,null,t,r):Jt(n,e.child,t,r)}function Xs(e,n,t,r,i){t=t.render;var l=n.ref;return qt(n,i),r=ya(e,n,t,r,l,i),t=xa(),e!==null&&!Me?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~i,Dn(e,n,i)):(ue&&t&&la(n),n.flags|=1,Le(e,n,r,i),n.child)}function Gs(e,n,t,r,i){if(e===null){var l=t.type;return typeof l=="function"&&!Ia(l)&&l.defaultProps===void 0&&t.compare===null&&t.defaultProps===void 0?(n.tag=15,n.type=l,Kp(e,n,l,r,i)):(e=Vi(t.type,null,r,n,n.mode,i),e.ref=n.ref,e.return=n,n.child=e)}if(l=e.child,!(e.lanes&i)){var o=l.memoizedProps;if(t=t.compare,t=t!==null?t:Vr,t(o,r)&&e.ref===n.ref)return Dn(e,n,i)}return n.flags|=1,e=Jn(l,r),e.ref=n.ref,e.return=n,n.child=e}function Kp(e,n,t,r,i){if(e!==null){var l=e.memoizedProps;if(Vr(l,r)&&e.ref===n.ref)if(Me=!1,n.pendingProps=r=l,(e.lanes&i)!==0)e.flags&131072&&(Me=!0);else return n.lanes=e.lanes,Dn(e,n,i)}return hu(e,n,t,r,i)}function Xp(e,n,t){var r=n.pendingProps,i=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(n.mode&1))n.memoizedState={baseLanes:0,cachePool:null,transitions:null},te($t,We),We|=t;else{if(!(t&1073741824))return e=l!==null?l.baseLanes|t:t,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:e,cachePool:null,transitions:null},n.updateQueue=null,te($t,We),We|=e,null;n.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:t,te($t,We),We|=r}else l!==null?(r=l.baseLanes|t,n.memoizedState=null):r=t,te($t,We),We|=r;return Le(e,n,i,t),n.child}function Gp(e,n){var t=n.ref;(e===null&&t!==null||e!==null&&e.ref!==t)&&(n.flags|=512,n.flags|=2097152)}function hu(e,n,t,r,i){var l=Be(t)?gt:_e.current;return l=Gt(n,l),qt(n,i),t=ya(e,n,t,r,l,i),r=xa(),e!==null&&!Me?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~i,Dn(e,n,i)):(ue&&r&&la(n),n.flags|=1,Le(e,n,t,i),n.child)}function Zs(e,n,t,r,i){if(Be(t)){var l=!0;il(n)}else l=!1;if(qt(n,i),n.stateNode===null)Ui(e,n),Ep(n,t,r),pu(n,t,r,i),r=!0;else if(e===null){var o=n.stateNode,u=n.memoizedProps;o.props=u;var a=o.context,s=t.contextType;typeof s=="object"&&s!==null?s=rn(s):(s=Be(t)?gt:_e.current,s=Gt(n,s));var f=t.getDerivedStateFromProps,c=typeof f=="function"||typeof o.getSnapshotBeforeUpdate=="function";c||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(u!==r||a!==s)&&Hs(n,o,r,s),Un=!1;var p=n.memoizedState;o.state=p,sl(n,r,o,i),a=n.memoizedState,u!==r||p!==a||Oe.current||Un?(typeof f=="function"&&(fu(n,t,f,r),a=n.memoizedState),(u=Un||$s(n,t,u,r,p,a,s))?(c||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(n.flags|=4194308)):(typeof o.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=r,n.memoizedState=a),o.props=r,o.state=a,o.context=s,r=u):(typeof o.componentDidMount=="function"&&(n.flags|=4194308),r=!1)}else{o=n.stateNode,Sp(e,n),u=n.memoizedProps,s=n.type===n.elementType?u:sn(n.type,u),o.props=s,c=n.pendingProps,p=o.context,a=t.contextType,typeof a=="object"&&a!==null?a=rn(a):(a=Be(t)?gt:_e.current,a=Gt(n,a));var d=t.getDerivedStateFromProps;(f=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(u!==c||p!==a)&&Hs(n,o,r,a),Un=!1,p=n.memoizedState,o.state=p,sl(n,r,o,i);var g=n.memoizedState;u!==c||p!==g||Oe.current||Un?(typeof d=="function"&&(fu(n,t,d,r),g=n.memoizedState),(s=Un||$s(n,t,s,r,p,g,a)||!1)?(f||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,g,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,g,a)),typeof o.componentDidUpdate=="function"&&(n.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof o.componentDidUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(n.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(n.flags|=1024),n.memoizedProps=r,n.memoizedState=g),o.props=r,o.state=g,o.context=a,r=s):(typeof o.componentDidUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(n.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(n.flags|=1024),r=!1)}return mu(e,n,t,r,l,i)}function mu(e,n,t,r,i,l){Gp(e,n);var o=(n.flags&128)!==0;if(!r&&!o)return i&&Fs(n,t,!1),Dn(e,n,l);r=n.stateNode,Og.current=n;var u=o&&typeof t.getDerivedStateFromError!="function"?null:r.render();return n.flags|=1,e!==null&&o?(n.child=Jt(n,e.child,null,l),n.child=Jt(n,null,u,l)):Le(e,n,u,l),n.memoizedState=r.state,i&&Fs(n,t,!0),n.child}function Zp(e){var n=e.stateNode;n.pendingContext?Ns(e,n.pendingContext,n.pendingContext!==n.context):n.context&&Ns(e,n.context,!1),da(e,n.containerInfo)}function Js(e,n,t,r,i){return Zt(),ua(i),n.flags|=256,Le(e,n,t,r),n.child}var gu={dehydrated:null,treeContext:null,retryLane:0};function yu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Jp(e,n,t){var r=n.pendingProps,i=ae.current,l=!1,o=(n.flags&128)!==0,u;if((u=o)||(u=e!==null&&e.memoizedState===null?!1:(i&2)!==0),u?(l=!0,n.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),te(ae,i&1),e===null)return su(n),e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(n.mode&1?e.data==="$!"?n.lanes=8:n.lanes=1073741824:n.lanes=1,null):(o=r.children,e=r.fallback,l?(r=n.mode,l=n.child,o={mode:"hidden",children:o},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=o):l=Al(o,r,0,null),e=mt(e,r,t,null),l.return=n,e.return=n,l.sibling=e,n.child=l,n.child.memoizedState=yu(t),n.memoizedState=gu,e):wa(n,o));if(i=e.memoizedState,i!==null&&(u=i.dehydrated,u!==null))return Bg(e,n,o,r,u,i,t);if(l){l=r.fallback,o=n.mode,i=e.child,u=i.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&n.child!==i?(r=n.child,r.childLanes=0,r.pendingProps=a,n.deletions=null):(r=Jn(i,a),r.subtreeFlags=i.subtreeFlags&14680064),u!==null?l=Jn(u,l):(l=mt(l,o,t,null),l.flags|=2),l.return=n,r.return=n,r.sibling=l,n.child=r,r=l,l=n.child,o=e.child.memoizedState,o=o===null?yu(t):{baseLanes:o.baseLanes|t,cachePool:null,transitions:o.transitions},l.memoizedState=o,l.childLanes=e.childLanes&~t,n.memoizedState=gu,r}return l=e.child,e=l.sibling,r=Jn(l,{mode:"visible",children:r.children}),!(n.mode&1)&&(r.lanes=t),r.return=n,r.sibling=null,e!==null&&(t=n.deletions,t===null?(n.deletions=[e],n.flags|=16):t.push(e)),n.child=r,n.memoizedState=null,r}function wa(e,n){return n=Al({mode:"visible",children:n},e.mode,0,null),n.return=e,e.child=n}function Ti(e,n,t,r){return r!==null&&ua(r),Jt(n,e.child,null,t),e=wa(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function Bg(e,n,t,r,i,l,o){if(t)return n.flags&256?(n.flags&=-257,r=po(Error(I(422))),Ti(e,n,o,r)):n.memoizedState!==null?(n.child=e.child,n.flags|=128,null):(l=r.fallback,i=n.mode,r=Al({mode:"visible",children:r.children},i,0,null),l=mt(l,i,o,null),l.flags|=2,r.return=n,l.return=n,r.sibling=l,n.child=r,n.mode&1&&Jt(n,e.child,null,o),n.child.memoizedState=yu(o),n.memoizedState=gu,l);if(!(n.mode&1))return Ti(e,n,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var u=r.dgst;return r=u,l=Error(I(419)),r=po(l,r,void 0),Ti(e,n,o,r)}if(u=(o&e.childLanes)!==0,Me||u){if(r=we,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==l.retryLane&&(l.retryLane=i,An(e,i),dn(r,e,i,-1))}return Pa(),r=po(Error(I(421))),Ti(e,n,o,r)}return i.data==="$?"?(n.flags|=128,n.child=e.child,n=Jg.bind(null,e),i._reactRetry=n,null):(e=l.treeContext,Qe=Kn(i.nextSibling),qe=n,ue=!0,fn=null,e!==null&&(Ze[Je++]=In,Ze[Je++]=_n,Ze[Je++]=yt,In=e.id,_n=e.overflow,yt=n),n=wa(n,r.children),n.flags|=4096,n)}function ec(e,n,t){e.lanes|=n;var r=e.alternate;r!==null&&(r.lanes|=n),cu(e.return,n,t)}function ho(e,n,t,r,i){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:r,tail:t,tailMode:i}:(l.isBackwards=n,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=t,l.tailMode=i)}function ed(e,n,t){var r=n.pendingProps,i=r.revealOrder,l=r.tail;if(Le(e,n,r.children,t),r=ae.current,r&2)r=r&1|2,n.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ec(e,t,n);else if(e.tag===19)ec(e,t,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;e.sibling===null;){if(e.return===null||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(te(ae,r),!(n.mode&1))n.memoizedState=null;else switch(i){case"forwards":for(t=n.child,i=null;t!==null;)e=t.alternate,e!==null&&cl(e)===null&&(i=t),t=t.sibling;t=i,t===null?(i=n.child,n.child=null):(i=t.sibling,t.sibling=null),ho(n,!1,i,t,l);break;case"backwards":for(t=null,i=n.child,n.child=null;i!==null;){if(e=i.alternate,e!==null&&cl(e)===null){n.child=i;break}e=i.sibling,i.sibling=t,t=i,i=e}ho(n,!0,t,null,l);break;case"together":ho(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Ui(e,n){!(n.mode&1)&&e!==null&&(e.alternate=null,n.alternate=null,n.flags|=2)}function Dn(e,n,t){if(e!==null&&(n.dependencies=e.dependencies),kt|=n.lanes,!(t&n.childLanes))return null;if(e!==null&&n.child!==e.child)throw Error(I(153));if(n.child!==null){for(e=n.child,t=Jn(e,e.pendingProps),n.child=t,t.return=n;e.sibling!==null;)e=e.sibling,t=t.sibling=Jn(e,e.pendingProps),t.return=n;t.sibling=null}return n.child}function Ug(e,n,t){switch(n.tag){case 3:Zp(n),Zt();break;case 5:Pp(n);break;case 1:Be(n.type)&&il(n);break;case 4:da(n,n.stateNode.containerInfo);break;case 10:var r=n.type._context,i=n.memoizedProps.value;te(ul,r._currentValue),r._currentValue=i;break;case 13:if(r=n.memoizedState,r!==null)return r.dehydrated!==null?(te(ae,ae.current&1),n.flags|=128,null):t&n.child.childLanes?Jp(e,n,t):(te(ae,ae.current&1),e=Dn(e,n,t),e!==null?e.sibling:null);te(ae,ae.current&1);break;case 19:if(r=(t&n.childLanes)!==0,e.flags&128){if(r)return ed(e,n,t);n.flags|=128}if(i=n.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),te(ae,ae.current),r)break;return null;case 22:case 23:return n.lanes=0,Xp(e,n,t)}return Dn(e,n,t)}var nd,xu,td,rd;nd=function(e,n){for(var t=n.child;t!==null;){if(t.tag===5||t.tag===6)e.appendChild(t.stateNode);else if(t.tag!==4&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===n)break;for(;t.sibling===null;){if(t.return===null||t.return===n)return;t=t.return}t.sibling.return=t.return,t=t.sibling}};xu=function(){};td=function(e,n,t,r){var i=e.memoizedProps;if(i!==r){e=n.stateNode,dt(Sn.current);var l=null;switch(t){case"input":i=Bo(e,i),r=Bo(e,r),l=[];break;case"select":i=ce({},i,{value:void 0}),r=ce({},r,{value:void 0}),l=[];break;case"textarea":i=Ho(e,i),r=Ho(e,r),l=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=tl)}Wo(t,r);var o;t=null;for(s in i)if(!r.hasOwnProperty(s)&&i.hasOwnProperty(s)&&i[s]!=null)if(s==="style"){var u=i[s];for(o in u)u.hasOwnProperty(o)&&(t||(t={}),t[o]="")}else s!=="dangerouslySetInnerHTML"&&s!=="children"&&s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Fr.hasOwnProperty(s)?l||(l=[]):(l=l||[]).push(s,null));for(s in r){var a=r[s];if(u=i!=null?i[s]:void 0,r.hasOwnProperty(s)&&a!==u&&(a!=null||u!=null))if(s==="style")if(u){for(o in u)!u.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(t||(t={}),t[o]="");for(o in a)a.hasOwnProperty(o)&&u[o]!==a[o]&&(t||(t={}),t[o]=a[o])}else t||(l||(l=[]),l.push(s,t)),t=a;else s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,u=u?u.__html:void 0,a!=null&&u!==a&&(l=l||[]).push(s,a)):s==="children"?typeof a!="string"&&typeof a!="number"||(l=l||[]).push(s,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&(Fr.hasOwnProperty(s)?(a!=null&&s==="onScroll"&&ie("scroll",e),l||u===a||(l=[])):(l=l||[]).push(s,a))}t&&(l=l||[]).push("style",t);var s=l;(n.updateQueue=s)&&(n.flags|=4)}};rd=function(e,n,t,r){t!==r&&(n.flags|=4)};function yr(e,n){if(!ue)switch(e.tailMode){case"hidden":n=e.tail;for(var t=null;n!==null;)n.alternate!==null&&(t=n),n=n.sibling;t===null?e.tail=null:t.sibling=null;break;case"collapsed":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ze(e){var n=e.alternate!==null&&e.alternate.child===e.child,t=0,r=0;if(n)for(var i=e.child;i!==null;)t|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)t|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=t,n}function $g(e,n,t){var r=n.pendingProps;switch(oa(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ze(n),null;case 1:return Be(n.type)&&rl(),ze(n),null;case 3:return r=n.stateNode,er(),oe(Oe),oe(_e),ma(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ci(n)?n.flags|=4:e===null||e.memoizedState.isDehydrated&&!(n.flags&256)||(n.flags|=1024,fn!==null&&(zu(fn),fn=null))),xu(e,n),ze(n),null;case 5:ha(n);var i=dt(Kr.current);if(t=n.type,e!==null&&n.stateNode!=null)td(e,n,t,r,i),e.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!r){if(n.stateNode===null)throw Error(I(166));return ze(n),null}if(e=dt(Sn.current),Ci(n)){r=n.stateNode,t=n.type;var l=n.memoizedProps;switch(r[vn]=n,r[qr]=l,e=(n.mode&1)!==0,t){case"dialog":ie("cancel",r),ie("close",r);break;case"iframe":case"object":case"embed":ie("load",r);break;case"video":case"audio":for(i=0;i<Er.length;i++)ie(Er[i],r);break;case"source":ie("error",r);break;case"img":case"image":case"link":ie("error",r),ie("load",r);break;case"details":ie("toggle",r);break;case"input":ss(r,l),ie("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},ie("invalid",r);break;case"textarea":fs(r,l),ie("invalid",r)}Wo(t,l),i=null;for(var o in l)if(l.hasOwnProperty(o)){var u=l[o];o==="children"?typeof u=="string"?r.textContent!==u&&(l.suppressHydrationWarning!==!0&&Si(r.textContent,u,e),i=["children",u]):typeof u=="number"&&r.textContent!==""+u&&(l.suppressHydrationWarning!==!0&&Si(r.textContent,u,e),i=["children",""+u]):Fr.hasOwnProperty(o)&&u!=null&&o==="onScroll"&&ie("scroll",r)}switch(t){case"input":hi(r),cs(r,l,!0);break;case"textarea":hi(r),ps(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=tl)}r=i,n.updateQueue=r,r!==null&&(n.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=bf(t)),e==="http://www.w3.org/1999/xhtml"?t==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(t,{is:r.is}):(e=o.createElement(t),t==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,t),e[vn]=n,e[qr]=r,nd(e,n,!1,!1),n.stateNode=e;e:{switch(o=Qo(t,r),t){case"dialog":ie("cancel",e),ie("close",e),i=r;break;case"iframe":case"object":case"embed":ie("load",e),i=r;break;case"video":case"audio":for(i=0;i<Er.length;i++)ie(Er[i],e);i=r;break;case"source":ie("error",e),i=r;break;case"img":case"image":case"link":ie("error",e),ie("load",e),i=r;break;case"details":ie("toggle",e),i=r;break;case"input":ss(e,r),i=Bo(e,r),ie("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=ce({},r,{value:void 0}),ie("invalid",e);break;case"textarea":fs(e,r),i=Ho(e,r),ie("invalid",e);break;default:i=r}Wo(t,i),u=i;for(l in u)if(u.hasOwnProperty(l)){var a=u[l];l==="style"?Af(e,a):l==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Lf(e,a)):l==="children"?typeof a=="string"?(t!=="textarea"||a!=="")&&Mr(e,a):typeof a=="number"&&Mr(e,""+a):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(Fr.hasOwnProperty(l)?a!=null&&l==="onScroll"&&ie("scroll",e):a!=null&&Wu(e,l,a,o))}switch(t){case"input":hi(e),cs(e,r,!1);break;case"textarea":hi(e),ps(e);break;case"option":r.value!=null&&e.setAttribute("value",""+et(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?Ht(e,!!r.multiple,l,!1):r.defaultValue!=null&&Ht(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=tl)}switch(t){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(n.flags|=4)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return ze(n),null;case 6:if(e&&n.stateNode!=null)rd(e,n,e.memoizedProps,r);else{if(typeof r!="string"&&n.stateNode===null)throw Error(I(166));if(t=dt(Kr.current),dt(Sn.current),Ci(n)){if(r=n.stateNode,t=n.memoizedProps,r[vn]=n,(l=r.nodeValue!==t)&&(e=qe,e!==null))switch(e.tag){case 3:Si(r.nodeValue,t,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Si(r.nodeValue,t,(e.mode&1)!==0)}l&&(n.flags|=4)}else r=(t.nodeType===9?t:t.ownerDocument).createTextNode(r),r[vn]=n,n.stateNode=r}return ze(n),null;case 13:if(oe(ae),r=n.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ue&&Qe!==null&&n.mode&1&&!(n.flags&128))vp(),Zt(),n.flags|=98560,l=!1;else if(l=Ci(n),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(I(318));if(l=n.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(I(317));l[vn]=n}else Zt(),!(n.flags&128)&&(n.memoizedState=null),n.flags|=4;ze(n),l=!1}else fn!==null&&(zu(fn),fn=null),l=!0;if(!l)return n.flags&65536?n:null}return n.flags&128?(n.lanes=t,n):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(n.child.flags|=8192,n.mode&1&&(e===null||ae.current&1?ke===0&&(ke=3):Pa())),n.updateQueue!==null&&(n.flags|=4),ze(n),null);case 4:return er(),xu(e,n),e===null&&Wr(n.stateNode.containerInfo),ze(n),null;case 10:return ca(n.type._context),ze(n),null;case 17:return Be(n.type)&&rl(),ze(n),null;case 19:if(oe(ae),l=n.memoizedState,l===null)return ze(n),null;if(r=(n.flags&128)!==0,o=l.rendering,o===null)if(r)yr(l,!1);else{if(ke!==0||e!==null&&e.flags&128)for(e=n.child;e!==null;){if(o=cl(e),o!==null){for(n.flags|=128,yr(l,!1),r=o.updateQueue,r!==null&&(n.updateQueue=r,n.flags|=4),n.subtreeFlags=0,r=t,t=n.child;t!==null;)l=t,e=r,l.flags&=14680066,o=l.alternate,o===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=o.childLanes,l.lanes=o.lanes,l.child=o.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=o.memoizedProps,l.memoizedState=o.memoizedState,l.updateQueue=o.updateQueue,l.type=o.type,e=o.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t=t.sibling;return te(ae,ae.current&1|2),n.child}e=e.sibling}l.tail!==null&&de()>tr&&(n.flags|=128,r=!0,yr(l,!1),n.lanes=4194304)}else{if(!r)if(e=cl(o),e!==null){if(n.flags|=128,r=!0,t=e.updateQueue,t!==null&&(n.updateQueue=t,n.flags|=4),yr(l,!0),l.tail===null&&l.tailMode==="hidden"&&!o.alternate&&!ue)return ze(n),null}else 2*de()-l.renderingStartTime>tr&&t!==1073741824&&(n.flags|=128,r=!0,yr(l,!1),n.lanes=4194304);l.isBackwards?(o.sibling=n.child,n.child=o):(t=l.last,t!==null?t.sibling=o:n.child=o,l.last=o)}return l.tail!==null?(n=l.tail,l.rendering=n,l.tail=n.sibling,l.renderingStartTime=de(),n.sibling=null,t=ae.current,te(ae,r?t&1|2:t&1),n):(ze(n),null);case 22:case 23:return za(),r=n.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(n.flags|=8192),r&&n.mode&1?We&1073741824&&(ze(n),n.subtreeFlags&6&&(n.flags|=8192)):ze(n),null;case 24:return null;case 25:return null}throw Error(I(156,n.tag))}function Hg(e,n){switch(oa(n),n.tag){case 1:return Be(n.type)&&rl(),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return er(),oe(Oe),oe(_e),ma(),e=n.flags,e&65536&&!(e&128)?(n.flags=e&-65537|128,n):null;case 5:return ha(n),null;case 13:if(oe(ae),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(I(340));Zt()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return oe(ae),null;case 4:return er(),null;case 10:return ca(n.type._context),null;case 22:case 23:return za(),null;case 24:return null;default:return null}}var zi=!1,Pe=!1,Vg=typeof WeakSet=="function"?WeakSet:Set,j=null;function Ut(e,n){var t=e.ref;if(t!==null)if(typeof t=="function")try{t(null)}catch(r){fe(e,n,r)}else t.current=null}function ku(e,n,t){try{t()}catch(r){fe(e,n,r)}}var nc=!1;function Wg(e,n){if(tu=Ji,e=up(),ia(e)){if("selectionStart"in e)var t={start:e.selectionStart,end:e.selectionEnd};else e:{t=(t=e.ownerDocument)&&t.defaultView||window;var r=t.getSelection&&t.getSelection();if(r&&r.rangeCount!==0){t=r.anchorNode;var i=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{t.nodeType,l.nodeType}catch{t=null;break e}var o=0,u=-1,a=-1,s=0,f=0,c=e,p=null;n:for(;;){for(var d;c!==t||i!==0&&c.nodeType!==3||(u=o+i),c!==l||r!==0&&c.nodeType!==3||(a=o+r),c.nodeType===3&&(o+=c.nodeValue.length),(d=c.firstChild)!==null;)p=c,c=d;for(;;){if(c===e)break n;if(p===t&&++s===i&&(u=o),p===l&&++f===r&&(a=o),(d=c.nextSibling)!==null)break;c=p,p=c.parentNode}c=d}t=u===-1||a===-1?null:{start:u,end:a}}else t=null}t=t||{start:0,end:0}}else t=null;for(ru={focusedElem:e,selectionRange:t},Ji=!1,j=n;j!==null;)if(n=j,e=n.child,(n.subtreeFlags&1028)!==0&&e!==null)e.return=n,j=e;else for(;j!==null;){n=j;try{var g=n.alternate;if(n.flags&1024)switch(n.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var v=g.memoizedProps,E=g.memoizedState,h=n.stateNode,m=h.getSnapshotBeforeUpdate(n.elementType===n.type?v:sn(n.type,v),E);h.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var y=n.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(I(163))}}catch(T){fe(n,n.return,T)}if(e=n.sibling,e!==null){e.return=n.return,j=e;break}j=n.return}return g=nc,nc=!1,g}function Lr(e,n,t){var r=n.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var l=i.destroy;i.destroy=void 0,l!==void 0&&ku(n,t,l)}i=i.next}while(i!==r)}}function Ll(e,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var t=n=n.next;do{if((t.tag&e)===e){var r=t.create;t.destroy=r()}t=t.next}while(t!==n)}}function vu(e){var n=e.ref;if(n!==null){var t=e.stateNode;switch(e.tag){case 5:e=t;break;default:e=t}typeof n=="function"?n(e):n.current=e}}function id(e){var n=e.alternate;n!==null&&(e.alternate=null,id(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&(delete n[vn],delete n[qr],delete n[ou],delete n[Pg],delete n[Ig])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ld(e){return e.tag===5||e.tag===3||e.tag===4}function tc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ld(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function wu(e,n,t){var r=e.tag;if(r===5||r===6)e=e.stateNode,n?t.nodeType===8?t.parentNode.insertBefore(e,n):t.insertBefore(e,n):(t.nodeType===8?(n=t.parentNode,n.insertBefore(e,t)):(n=t,n.appendChild(e)),t=t._reactRootContainer,t!=null||n.onclick!==null||(n.onclick=tl));else if(r!==4&&(e=e.child,e!==null))for(wu(e,n,t),e=e.sibling;e!==null;)wu(e,n,t),e=e.sibling}function Su(e,n,t){var r=e.tag;if(r===5||r===6)e=e.stateNode,n?t.insertBefore(e,n):t.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Su(e,n,t),e=e.sibling;e!==null;)Su(e,n,t),e=e.sibling}var Se=null,cn=!1;function Mn(e,n,t){for(t=t.child;t!==null;)od(e,n,t),t=t.sibling}function od(e,n,t){if(wn&&typeof wn.onCommitFiberUnmount=="function")try{wn.onCommitFiberUnmount(Cl,t)}catch{}switch(t.tag){case 5:Pe||Ut(t,n);case 6:var r=Se,i=cn;Se=null,Mn(e,n,t),Se=r,cn=i,Se!==null&&(cn?(e=Se,t=t.stateNode,e.nodeType===8?e.parentNode.removeChild(t):e.removeChild(t)):Se.removeChild(t.stateNode));break;case 18:Se!==null&&(cn?(e=Se,t=t.stateNode,e.nodeType===8?oo(e.parentNode,t):e.nodeType===1&&oo(e,t),$r(e)):oo(Se,t.stateNode));break;case 4:r=Se,i=cn,Se=t.stateNode.containerInfo,cn=!0,Mn(e,n,t),Se=r,cn=i;break;case 0:case 11:case 14:case 15:if(!Pe&&(r=t.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var l=i,o=l.destroy;l=l.tag,o!==void 0&&(l&2||l&4)&&ku(t,n,o),i=i.next}while(i!==r)}Mn(e,n,t);break;case 1:if(!Pe&&(Ut(t,n),r=t.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=t.memoizedProps,r.state=t.memoizedState,r.componentWillUnmount()}catch(u){fe(t,n,u)}Mn(e,n,t);break;case 21:Mn(e,n,t);break;case 22:t.mode&1?(Pe=(r=Pe)||t.memoizedState!==null,Mn(e,n,t),Pe=r):Mn(e,n,t);break;default:Mn(e,n,t)}}function rc(e){var n=e.updateQueue;if(n!==null){e.updateQueue=null;var t=e.stateNode;t===null&&(t=e.stateNode=new Vg),n.forEach(function(r){var i=ey.bind(null,e,r);t.has(r)||(t.add(r),r.then(i,i))})}}function an(e,n){var t=n.deletions;if(t!==null)for(var r=0;r<t.length;r++){var i=t[r];try{var l=e,o=n,u=o;e:for(;u!==null;){switch(u.tag){case 5:Se=u.stateNode,cn=!1;break e;case 3:Se=u.stateNode.containerInfo,cn=!0;break e;case 4:Se=u.stateNode.containerInfo,cn=!0;break e}u=u.return}if(Se===null)throw Error(I(160));od(l,o,i),Se=null,cn=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(s){fe(i,n,s)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)ud(n,e),n=n.sibling}function ud(e,n){var t=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(an(n,e),yn(e),r&4){try{Lr(3,e,e.return),Ll(3,e)}catch(v){fe(e,e.return,v)}try{Lr(5,e,e.return)}catch(v){fe(e,e.return,v)}}break;case 1:an(n,e),yn(e),r&512&&t!==null&&Ut(t,t.return);break;case 5:if(an(n,e),yn(e),r&512&&t!==null&&Ut(t,t.return),e.flags&32){var i=e.stateNode;try{Mr(i,"")}catch(v){fe(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var l=e.memoizedProps,o=t!==null?t.memoizedProps:l,u=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{u==="input"&&l.type==="radio"&&l.name!=null&&If(i,l),Qo(u,o);var s=Qo(u,l);for(o=0;o<a.length;o+=2){var f=a[o],c=a[o+1];f==="style"?Af(i,c):f==="dangerouslySetInnerHTML"?Lf(i,c):f==="children"?Mr(i,c):Wu(i,f,c,s)}switch(u){case"input":Uo(i,l);break;case"textarea":_f(i,l);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!l.multiple;var d=l.value;d!=null?Ht(i,!!l.multiple,d,!1):p!==!!l.multiple&&(l.defaultValue!=null?Ht(i,!!l.multiple,l.defaultValue,!0):Ht(i,!!l.multiple,l.multiple?[]:"",!1))}i[qr]=l}catch(v){fe(e,e.return,v)}}break;case 6:if(an(n,e),yn(e),r&4){if(e.stateNode===null)throw Error(I(162));i=e.stateNode,l=e.memoizedProps;try{i.nodeValue=l}catch(v){fe(e,e.return,v)}}break;case 3:if(an(n,e),yn(e),r&4&&t!==null&&t.memoizedState.isDehydrated)try{$r(n.containerInfo)}catch(v){fe(e,e.return,v)}break;case 4:an(n,e),yn(e);break;case 13:an(n,e),yn(e),i=e.child,i.flags&8192&&(l=i.memoizedState!==null,i.stateNode.isHidden=l,!l||i.alternate!==null&&i.alternate.memoizedState!==null||(Ea=de())),r&4&&rc(e);break;case 22:if(f=t!==null&&t.memoizedState!==null,e.mode&1?(Pe=(s=Pe)||f,an(n,e),Pe=s):an(n,e),yn(e),r&8192){if(s=e.memoizedState!==null,(e.stateNode.isHidden=s)&&!f&&e.mode&1)for(j=e,f=e.child;f!==null;){for(c=j=f;j!==null;){switch(p=j,d=p.child,p.tag){case 0:case 11:case 14:case 15:Lr(4,p,p.return);break;case 1:Ut(p,p.return);var g=p.stateNode;if(typeof g.componentWillUnmount=="function"){r=p,t=p.return;try{n=r,g.props=n.memoizedProps,g.state=n.memoizedState,g.componentWillUnmount()}catch(v){fe(r,t,v)}}break;case 5:Ut(p,p.return);break;case 22:if(p.memoizedState!==null){lc(c);continue}}d!==null?(d.return=p,j=d):lc(c)}f=f.sibling}e:for(f=null,c=e;;){if(c.tag===5){if(f===null){f=c;try{i=c.stateNode,s?(l=i.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(u=c.stateNode,a=c.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,u.style.display=Rf("display",o))}catch(v){fe(e,e.return,v)}}}else if(c.tag===6){if(f===null)try{c.stateNode.nodeValue=s?"":c.memoizedProps}catch(v){fe(e,e.return,v)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;f===c&&(f=null),c=c.return}f===c&&(f=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:an(n,e),yn(e),r&4&&rc(e);break;case 21:break;default:an(n,e),yn(e)}}function yn(e){var n=e.flags;if(n&2){try{e:{for(var t=e.return;t!==null;){if(ld(t)){var r=t;break e}t=t.return}throw Error(I(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Mr(i,""),r.flags&=-33);var l=tc(e);Su(e,l,i);break;case 3:case 4:var o=r.stateNode.containerInfo,u=tc(e);wu(e,u,o);break;default:throw Error(I(161))}}catch(a){fe(e,e.return,a)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function Qg(e,n,t){j=e,ad(e)}function ad(e,n,t){for(var r=(e.mode&1)!==0;j!==null;){var i=j,l=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||zi;if(!o){var u=i.alternate,a=u!==null&&u.memoizedState!==null||Pe;u=zi;var s=Pe;if(zi=o,(Pe=a)&&!s)for(j=i;j!==null;)o=j,a=o.child,o.tag===22&&o.memoizedState!==null?oc(i):a!==null?(a.return=o,j=a):oc(i);for(;l!==null;)j=l,ad(l),l=l.sibling;j=i,zi=u,Pe=s}ic(e)}else i.subtreeFlags&8772&&l!==null?(l.return=i,j=l):ic(e)}}function ic(e){for(;j!==null;){var n=j;if(n.flags&8772){var t=n.alternate;try{if(n.flags&8772)switch(n.tag){case 0:case 11:case 15:Pe||Ll(5,n);break;case 1:var r=n.stateNode;if(n.flags&4&&!Pe)if(t===null)r.componentDidMount();else{var i=n.elementType===n.type?t.memoizedProps:sn(n.type,t.memoizedProps);r.componentDidUpdate(i,t.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=n.updateQueue;l!==null&&Us(n,l,r);break;case 3:var o=n.updateQueue;if(o!==null){if(t=null,n.child!==null)switch(n.child.tag){case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}Us(n,o,t)}break;case 5:var u=n.stateNode;if(t===null&&n.flags&4){t=u;var a=n.memoizedProps;switch(n.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&t.focus();break;case"img":a.src&&(t.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(n.memoizedState===null){var s=n.alternate;if(s!==null){var f=s.memoizedState;if(f!==null){var c=f.dehydrated;c!==null&&$r(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(I(163))}Pe||n.flags&512&&vu(n)}catch(p){fe(n,n.return,p)}}if(n===e){j=null;break}if(t=n.sibling,t!==null){t.return=n.return,j=t;break}j=n.return}}function lc(e){for(;j!==null;){var n=j;if(n===e){j=null;break}var t=n.sibling;if(t!==null){t.return=n.return,j=t;break}j=n.return}}function oc(e){for(;j!==null;){var n=j;try{switch(n.tag){case 0:case 11:case 15:var t=n.return;try{Ll(4,n)}catch(a){fe(n,t,a)}break;case 1:var r=n.stateNode;if(typeof r.componentDidMount=="function"){var i=n.return;try{r.componentDidMount()}catch(a){fe(n,i,a)}}var l=n.return;try{vu(n)}catch(a){fe(n,l,a)}break;case 5:var o=n.return;try{vu(n)}catch(a){fe(n,o,a)}}}catch(a){fe(n,n.return,a)}if(n===e){j=null;break}var u=n.sibling;if(u!==null){u.return=n.return,j=u;break}j=n.return}}var qg=Math.ceil,dl=jn.ReactCurrentDispatcher,Sa=jn.ReactCurrentOwner,tn=jn.ReactCurrentBatchConfig,K=0,we=null,ge=null,Ce=0,We=0,$t=rt(0),ke=0,Jr=null,kt=0,Rl=0,Ca=0,Rr=null,Fe=null,Ea=0,tr=1/0,zn=null,hl=!1,Cu=null,Gn=null,Pi=!1,Wn=null,ml=0,Ar=0,Eu=null,$i=-1,Hi=0;function Ae(){return K&6?de():$i!==-1?$i:$i=de()}function Zn(e){return e.mode&1?K&2&&Ce!==0?Ce&-Ce:bg.transition!==null?(Hi===0&&(Hi=Wf()),Hi):(e=X,e!==0||(e=window.event,e=e===void 0?16:Zf(e.type)),e):1}function dn(e,n,t,r){if(50<Ar)throw Ar=0,Eu=null,Error(I(185));ri(e,t,r),(!(K&2)||e!==we)&&(e===we&&(!(K&2)&&(Rl|=t),ke===4&&Hn(e,Ce)),Ue(e,r),t===1&&K===0&&!(n.mode&1)&&(tr=de()+500,Il&&it()))}function Ue(e,n){var t=e.callbackNode;bm(e,n);var r=Zi(e,e===we?Ce:0);if(r===0)t!==null&&ms(t),e.callbackNode=null,e.callbackPriority=0;else if(n=r&-r,e.callbackPriority!==n){if(t!=null&&ms(t),n===1)e.tag===0?_g(uc.bind(null,e)):yp(uc.bind(null,e)),Tg(function(){!(K&6)&&it()}),t=null;else{switch(Qf(r)){case 1:t=Xu;break;case 4:t=Hf;break;case 16:t=Gi;break;case 536870912:t=Vf;break;default:t=Gi}t=gd(t,sd.bind(null,e))}e.callbackPriority=n,e.callbackNode=t}}function sd(e,n){if($i=-1,Hi=0,K&6)throw Error(I(327));var t=e.callbackNode;if(Yt()&&e.callbackNode!==t)return null;var r=Zi(e,e===we?Ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||n)n=gl(e,r);else{n=r;var i=K;K|=2;var l=fd();(we!==e||Ce!==n)&&(zn=null,tr=de()+500,ht(e,n));do try{Xg();break}catch(u){cd(e,u)}while(1);sa(),dl.current=l,K=i,ge!==null?n=0:(we=null,Ce=0,n=ke)}if(n!==0){if(n===2&&(i=Go(e),i!==0&&(r=i,n=Tu(e,i))),n===1)throw t=Jr,ht(e,0),Hn(e,r),Ue(e,de()),t;if(n===6)Hn(e,r);else{if(i=e.current.alternate,!(r&30)&&!Yg(i)&&(n=gl(e,r),n===2&&(l=Go(e),l!==0&&(r=l,n=Tu(e,l))),n===1))throw t=Jr,ht(e,0),Hn(e,r),Ue(e,de()),t;switch(e.finishedWork=i,e.finishedLanes=r,n){case 0:case 1:throw Error(I(345));case 2:st(e,Fe,zn);break;case 3:if(Hn(e,r),(r&130023424)===r&&(n=Ea+500-de(),10<n)){if(Zi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Ae(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=lu(st.bind(null,e,Fe,zn),n);break}st(e,Fe,zn);break;case 4:if(Hn(e,r),(r&4194240)===r)break;for(n=e.eventTimes,i=-1;0<r;){var o=31-pn(r);l=1<<o,o=n[o],o>i&&(i=o),r&=~l}if(r=i,r=de()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*qg(r/1960))-r,10<r){e.timeoutHandle=lu(st.bind(null,e,Fe,zn),r);break}st(e,Fe,zn);break;case 5:st(e,Fe,zn);break;default:throw Error(I(329))}}}return Ue(e,de()),e.callbackNode===t?sd.bind(null,e):null}function Tu(e,n){var t=Rr;return e.current.memoizedState.isDehydrated&&(ht(e,n).flags|=256),e=gl(e,n),e!==2&&(n=Fe,Fe=t,n!==null&&zu(n)),e}function zu(e){Fe===null?Fe=e:Fe.push.apply(Fe,e)}function Yg(e){for(var n=e;;){if(n.flags&16384){var t=n.updateQueue;if(t!==null&&(t=t.stores,t!==null))for(var r=0;r<t.length;r++){var i=t[r],l=i.getSnapshot;i=i.value;try{if(!mn(l(),i))return!1}catch{return!1}}}if(t=n.child,n.subtreeFlags&16384&&t!==null)t.return=n,n=t;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function Hn(e,n){for(n&=~Ca,n&=~Rl,e.suspendedLanes|=n,e.pingedLanes&=~n,e=e.expirationTimes;0<n;){var t=31-pn(n),r=1<<t;e[t]=-1,n&=~r}}function uc(e){if(K&6)throw Error(I(327));Yt();var n=Zi(e,0);if(!(n&1))return Ue(e,de()),null;var t=gl(e,n);if(e.tag!==0&&t===2){var r=Go(e);r!==0&&(n=r,t=Tu(e,r))}if(t===1)throw t=Jr,ht(e,0),Hn(e,n),Ue(e,de()),t;if(t===6)throw Error(I(345));return e.finishedWork=e.current.alternate,e.finishedLanes=n,st(e,Fe,zn),Ue(e,de()),null}function Ta(e,n){var t=K;K|=1;try{return e(n)}finally{K=t,K===0&&(tr=de()+500,Il&&it())}}function vt(e){Wn!==null&&Wn.tag===0&&!(K&6)&&Yt();var n=K;K|=1;var t=tn.transition,r=X;try{if(tn.transition=null,X=1,e)return e()}finally{X=r,tn.transition=t,K=n,!(K&6)&&it()}}function za(){We=$t.current,oe($t)}function ht(e,n){e.finishedWork=null,e.finishedLanes=0;var t=e.timeoutHandle;if(t!==-1&&(e.timeoutHandle=-1,Eg(t)),ge!==null)for(t=ge.return;t!==null;){var r=t;switch(oa(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&rl();break;case 3:er(),oe(Oe),oe(_e),ma();break;case 5:ha(r);break;case 4:er();break;case 13:oe(ae);break;case 19:oe(ae);break;case 10:ca(r.type._context);break;case 22:case 23:za()}t=t.return}if(we=e,ge=e=Jn(e.current,null),Ce=We=n,ke=0,Jr=null,Ca=Rl=kt=0,Fe=Rr=null,pt!==null){for(n=0;n<pt.length;n++)if(t=pt[n],r=t.interleaved,r!==null){t.interleaved=null;var i=r.next,l=t.pending;if(l!==null){var o=l.next;l.next=i,r.next=o}t.pending=r}pt=null}return e}function cd(e,n){do{var t=ge;try{if(sa(),Oi.current=pl,fl){for(var r=se.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}fl=!1}if(xt=0,ve=xe=se=null,br=!1,Xr=0,Sa.current=null,t===null||t.return===null){ke=1,Jr=n,ge=null;break}e:{var l=e,o=t.return,u=t,a=n;if(n=Ce,u.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var s=a,f=u,c=f.tag;if(!(f.mode&1)&&(c===0||c===11||c===15)){var p=f.alternate;p?(f.updateQueue=p.updateQueue,f.memoizedState=p.memoizedState,f.lanes=p.lanes):(f.updateQueue=null,f.memoizedState=null)}var d=Ys(o);if(d!==null){d.flags&=-257,Ks(d,o,u,l,n),d.mode&1&&qs(l,s,n),n=d,a=s;var g=n.updateQueue;if(g===null){var v=new Set;v.add(a),n.updateQueue=v}else g.add(a);break e}else{if(!(n&1)){qs(l,s,n),Pa();break e}a=Error(I(426))}}else if(ue&&u.mode&1){var E=Ys(o);if(E!==null){!(E.flags&65536)&&(E.flags|=256),Ks(E,o,u,l,n),ua(nr(a,u));break e}}l=a=nr(a,u),ke!==4&&(ke=2),Rr===null?Rr=[l]:Rr.push(l),l=o;do{switch(l.tag){case 3:l.flags|=65536,n&=-n,l.lanes|=n;var h=qp(l,a,n);Bs(l,h);break e;case 1:u=a;var m=l.type,y=l.stateNode;if(!(l.flags&128)&&(typeof m.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Gn===null||!Gn.has(y)))){l.flags|=65536,n&=-n,l.lanes|=n;var T=Yp(l,u,n);Bs(l,T);break e}}l=l.return}while(l!==null)}dd(t)}catch(P){n=P,ge===t&&t!==null&&(ge=t=t.return);continue}break}while(1)}function fd(){var e=dl.current;return dl.current=pl,e===null?pl:e}function Pa(){(ke===0||ke===3||ke===2)&&(ke=4),we===null||!(kt&268435455)&&!(Rl&268435455)||Hn(we,Ce)}function gl(e,n){var t=K;K|=2;var r=fd();(we!==e||Ce!==n)&&(zn=null,ht(e,n));do try{Kg();break}catch(i){cd(e,i)}while(1);if(sa(),K=t,dl.current=r,ge!==null)throw Error(I(261));return we=null,Ce=0,ke}function Kg(){for(;ge!==null;)pd(ge)}function Xg(){for(;ge!==null&&!wm();)pd(ge)}function pd(e){var n=md(e.alternate,e,We);e.memoizedProps=e.pendingProps,n===null?dd(e):ge=n,Sa.current=null}function dd(e){var n=e;do{var t=n.alternate;if(e=n.return,n.flags&32768){if(t=Hg(t,n),t!==null){t.flags&=32767,ge=t;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ke=6,ge=null;return}}else if(t=$g(t,n,We),t!==null){ge=t;return}if(n=n.sibling,n!==null){ge=n;return}ge=n=e}while(n!==null);ke===0&&(ke=5)}function st(e,n,t){var r=X,i=tn.transition;try{tn.transition=null,X=1,Gg(e,n,t,r)}finally{tn.transition=i,X=r}return null}function Gg(e,n,t,r){do Yt();while(Wn!==null);if(K&6)throw Error(I(327));t=e.finishedWork;var i=e.finishedLanes;if(t===null)return null;if(e.finishedWork=null,e.finishedLanes=0,t===e.current)throw Error(I(177));e.callbackNode=null,e.callbackPriority=0;var l=t.lanes|t.childLanes;if(Lm(e,l),e===we&&(ge=we=null,Ce=0),!(t.subtreeFlags&2064)&&!(t.flags&2064)||Pi||(Pi=!0,gd(Gi,function(){return Yt(),null})),l=(t.flags&15990)!==0,t.subtreeFlags&15990||l){l=tn.transition,tn.transition=null;var o=X;X=1;var u=K;K|=4,Sa.current=null,Wg(e,t),ud(t,e),yg(ru),Ji=!!tu,ru=tu=null,e.current=t,Qg(t),Sm(),K=u,X=o,tn.transition=l}else e.current=t;if(Pi&&(Pi=!1,Wn=e,ml=i),l=e.pendingLanes,l===0&&(Gn=null),Tm(t.stateNode),Ue(e,de()),n!==null)for(r=e.onRecoverableError,t=0;t<n.length;t++)i=n[t],r(i.value,{componentStack:i.stack,digest:i.digest});if(hl)throw hl=!1,e=Cu,Cu=null,e;return ml&1&&e.tag!==0&&Yt(),l=e.pendingLanes,l&1?e===Eu?Ar++:(Ar=0,Eu=e):Ar=0,it(),null}function Yt(){if(Wn!==null){var e=Qf(ml),n=tn.transition,t=X;try{if(tn.transition=null,X=16>e?16:e,Wn===null)var r=!1;else{if(e=Wn,Wn=null,ml=0,K&6)throw Error(I(331));var i=K;for(K|=4,j=e.current;j!==null;){var l=j,o=l.child;if(j.flags&16){var u=l.deletions;if(u!==null){for(var a=0;a<u.length;a++){var s=u[a];for(j=s;j!==null;){var f=j;switch(f.tag){case 0:case 11:case 15:Lr(8,f,l)}var c=f.child;if(c!==null)c.return=f,j=c;else for(;j!==null;){f=j;var p=f.sibling,d=f.return;if(id(f),f===s){j=null;break}if(p!==null){p.return=d,j=p;break}j=d}}}var g=l.alternate;if(g!==null){var v=g.child;if(v!==null){g.child=null;do{var E=v.sibling;v.sibling=null,v=E}while(v!==null)}}j=l}}if(l.subtreeFlags&2064&&o!==null)o.return=l,j=o;else e:for(;j!==null;){if(l=j,l.flags&2048)switch(l.tag){case 0:case 11:case 15:Lr(9,l,l.return)}var h=l.sibling;if(h!==null){h.return=l.return,j=h;break e}j=l.return}}var m=e.current;for(j=m;j!==null;){o=j;var y=o.child;if(o.subtreeFlags&2064&&y!==null)y.return=o,j=y;else e:for(o=m;j!==null;){if(u=j,u.flags&2048)try{switch(u.tag){case 0:case 11:case 15:Ll(9,u)}}catch(P){fe(u,u.return,P)}if(u===o){j=null;break e}var T=u.sibling;if(T!==null){T.return=u.return,j=T;break e}j=u.return}}if(K=i,it(),wn&&typeof wn.onPostCommitFiberRoot=="function")try{wn.onPostCommitFiberRoot(Cl,e)}catch{}r=!0}return r}finally{X=t,tn.transition=n}}return!1}function ac(e,n,t){n=nr(t,n),n=qp(e,n,1),e=Xn(e,n,1),n=Ae(),e!==null&&(ri(e,1,n),Ue(e,n))}function fe(e,n,t){if(e.tag===3)ac(e,e,t);else for(;n!==null;){if(n.tag===3){ac(n,e,t);break}else if(n.tag===1){var r=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Gn===null||!Gn.has(r))){e=nr(t,e),e=Yp(n,e,1),n=Xn(n,e,1),e=Ae(),n!==null&&(ri(n,1,e),Ue(n,e));break}}n=n.return}}function Zg(e,n,t){var r=e.pingCache;r!==null&&r.delete(n),n=Ae(),e.pingedLanes|=e.suspendedLanes&t,we===e&&(Ce&t)===t&&(ke===4||ke===3&&(Ce&130023424)===Ce&&500>de()-Ea?ht(e,0):Ca|=t),Ue(e,n)}function hd(e,n){n===0&&(e.mode&1?(n=yi,yi<<=1,!(yi&130023424)&&(yi=4194304)):n=1);var t=Ae();e=An(e,n),e!==null&&(ri(e,n,t),Ue(e,t))}function Jg(e){var n=e.memoizedState,t=0;n!==null&&(t=n.retryLane),hd(e,t)}function ey(e,n){var t=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(t=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(I(314))}r!==null&&r.delete(n),hd(e,t)}var md;md=function(e,n,t){if(e!==null)if(e.memoizedProps!==n.pendingProps||Oe.current)Me=!0;else{if(!(e.lanes&t)&&!(n.flags&128))return Me=!1,Ug(e,n,t);Me=!!(e.flags&131072)}else Me=!1,ue&&n.flags&1048576&&xp(n,ol,n.index);switch(n.lanes=0,n.tag){case 2:var r=n.type;Ui(e,n),e=n.pendingProps;var i=Gt(n,_e.current);qt(n,t),i=ya(null,n,r,e,i,t);var l=xa();return n.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,Be(r)?(l=!0,il(n)):l=!1,n.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,pa(n),i.updater=_l,n.stateNode=i,i._reactInternals=n,pu(n,r,e,t),n=mu(null,n,r,!0,l,t)):(n.tag=0,ue&&l&&la(n),Le(null,n,i,t),n=n.child),n;case 16:r=n.elementType;e:{switch(Ui(e,n),e=n.pendingProps,i=r._init,r=i(r._payload),n.type=r,i=n.tag=ty(r),e=sn(r,e),i){case 0:n=hu(null,n,r,e,t);break e;case 1:n=Zs(null,n,r,e,t);break e;case 11:n=Xs(null,n,r,e,t);break e;case 14:n=Gs(null,n,r,sn(r.type,e),t);break e}throw Error(I(306,r,""))}return n;case 0:return r=n.type,i=n.pendingProps,i=n.elementType===r?i:sn(r,i),hu(e,n,r,i,t);case 1:return r=n.type,i=n.pendingProps,i=n.elementType===r?i:sn(r,i),Zs(e,n,r,i,t);case 3:e:{if(Zp(n),e===null)throw Error(I(387));r=n.pendingProps,l=n.memoizedState,i=l.element,Sp(e,n),sl(n,r,null,t);var o=n.memoizedState;if(r=o.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},n.updateQueue.baseState=l,n.memoizedState=l,n.flags&256){i=nr(Error(I(423)),n),n=Js(e,n,r,t,i);break e}else if(r!==i){i=nr(Error(I(424)),n),n=Js(e,n,r,t,i);break e}else for(Qe=Kn(n.stateNode.containerInfo.firstChild),qe=n,ue=!0,fn=null,t=zp(n,null,r,t),n.child=t;t;)t.flags=t.flags&-3|4096,t=t.sibling;else{if(Zt(),r===i){n=Dn(e,n,t);break e}Le(e,n,r,t)}n=n.child}return n;case 5:return Pp(n),e===null&&su(n),r=n.type,i=n.pendingProps,l=e!==null?e.memoizedProps:null,o=i.children,iu(r,i)?o=null:l!==null&&iu(r,l)&&(n.flags|=32),Gp(e,n),Le(e,n,o,t),n.child;case 6:return e===null&&su(n),null;case 13:return Jp(e,n,t);case 4:return da(n,n.stateNode.containerInfo),r=n.pendingProps,e===null?n.child=Jt(n,null,r,t):Le(e,n,r,t),n.child;case 11:return r=n.type,i=n.pendingProps,i=n.elementType===r?i:sn(r,i),Xs(e,n,r,i,t);case 7:return Le(e,n,n.pendingProps,t),n.child;case 8:return Le(e,n,n.pendingProps.children,t),n.child;case 12:return Le(e,n,n.pendingProps.children,t),n.child;case 10:e:{if(r=n.type._context,i=n.pendingProps,l=n.memoizedProps,o=i.value,te(ul,r._currentValue),r._currentValue=o,l!==null)if(mn(l.value,o)){if(l.children===i.children&&!Oe.current){n=Dn(e,n,t);break e}}else for(l=n.child,l!==null&&(l.return=n);l!==null;){var u=l.dependencies;if(u!==null){o=l.child;for(var a=u.firstContext;a!==null;){if(a.context===r){if(l.tag===1){a=bn(-1,t&-t),a.tag=2;var s=l.updateQueue;if(s!==null){s=s.shared;var f=s.pending;f===null?a.next=a:(a.next=f.next,f.next=a),s.pending=a}}l.lanes|=t,a=l.alternate,a!==null&&(a.lanes|=t),cu(l.return,t,n),u.lanes|=t;break}a=a.next}}else if(l.tag===10)o=l.type===n.type?null:l.child;else if(l.tag===18){if(o=l.return,o===null)throw Error(I(341));o.lanes|=t,u=o.alternate,u!==null&&(u.lanes|=t),cu(o,t,n),o=l.sibling}else o=l.child;if(o!==null)o.return=l;else for(o=l;o!==null;){if(o===n){o=null;break}if(l=o.sibling,l!==null){l.return=o.return,o=l;break}o=o.return}l=o}Le(e,n,i.children,t),n=n.child}return n;case 9:return i=n.type,r=n.pendingProps.children,qt(n,t),i=rn(i),r=r(i),n.flags|=1,Le(e,n,r,t),n.child;case 14:return r=n.type,i=sn(r,n.pendingProps),i=sn(r.type,i),Gs(e,n,r,i,t);case 15:return Kp(e,n,n.type,n.pendingProps,t);case 17:return r=n.type,i=n.pendingProps,i=n.elementType===r?i:sn(r,i),Ui(e,n),n.tag=1,Be(r)?(e=!0,il(n)):e=!1,qt(n,t),Ep(n,r,i),pu(n,r,i,t),mu(null,n,r,!0,e,t);case 19:return ed(e,n,t);case 22:return Xp(e,n,t)}throw Error(I(156,n.tag))};function gd(e,n){return $f(e,n)}function ny(e,n,t,r){this.tag=e,this.key=t,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nn(e,n,t,r){return new ny(e,n,t,r)}function Ia(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ty(e){if(typeof e=="function")return Ia(e)?1:0;if(e!=null){if(e=e.$$typeof,e===qu)return 11;if(e===Yu)return 14}return 2}function Jn(e,n){var t=e.alternate;return t===null?(t=nn(e.tag,n,e.key,e.mode),t.elementType=e.elementType,t.type=e.type,t.stateNode=e.stateNode,t.alternate=e,e.alternate=t):(t.pendingProps=n,t.type=e.type,t.flags=0,t.subtreeFlags=0,t.deletions=null),t.flags=e.flags&14680064,t.childLanes=e.childLanes,t.lanes=e.lanes,t.child=e.child,t.memoizedProps=e.memoizedProps,t.memoizedState=e.memoizedState,t.updateQueue=e.updateQueue,n=e.dependencies,t.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},t.sibling=e.sibling,t.index=e.index,t.ref=e.ref,t}function Vi(e,n,t,r,i,l){var o=2;if(r=e,typeof e=="function")Ia(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Rt:return mt(t.children,i,l,n);case Qu:o=8,i|=8;break;case No:return e=nn(12,t,n,i|2),e.elementType=No,e.lanes=l,e;case Fo:return e=nn(13,t,n,i),e.elementType=Fo,e.lanes=l,e;case Mo:return e=nn(19,t,n,i),e.elementType=Mo,e.lanes=l,e;case Tf:return Al(t,i,l,n);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Cf:o=10;break e;case Ef:o=9;break e;case qu:o=11;break e;case Yu:o=14;break e;case Bn:o=16,r=null;break e}throw Error(I(130,e==null?e:typeof e,""))}return n=nn(o,t,n,i),n.elementType=e,n.type=r,n.lanes=l,n}function mt(e,n,t,r){return e=nn(7,e,r,n),e.lanes=t,e}function Al(e,n,t,r){return e=nn(22,e,r,n),e.elementType=Tf,e.lanes=t,e.stateNode={isHidden:!1},e}function mo(e,n,t){return e=nn(6,e,null,n),e.lanes=t,e}function go(e,n,t){return n=nn(4,e.children!==null?e.children:[],e.key,n),n.lanes=t,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function ry(e,n,t,r,i){this.tag=n,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Kl(0),this.expirationTimes=Kl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Kl(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function _a(e,n,t,r,i,l,o,u,a){return e=new ry(e,n,t,u,a),n===1?(n=1,l===!0&&(n|=8)):n=0,l=nn(3,null,null,n),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:t,cache:null,transitions:null,pendingSuspenseBoundaries:null},pa(l),e}function iy(e,n,t){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Lt,key:r==null?null:""+r,children:e,containerInfo:n,implementation:t}}function yd(e){if(!e)return nt;e=e._reactInternals;e:{if(Ct(e)!==e||e.tag!==1)throw Error(I(170));var n=e;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(Be(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(I(171))}if(e.tag===1){var t=e.type;if(Be(t))return gp(e,t,n)}return n}function xd(e,n,t,r,i,l,o,u,a){return e=_a(t,r,!0,e,i,l,o,u,a),e.context=yd(null),t=e.current,r=Ae(),i=Zn(t),l=bn(r,i),l.callback=n??null,Xn(t,l,i),e.current.lanes=i,ri(e,i,r),Ue(e,r),e}function Dl(e,n,t,r){var i=n.current,l=Ae(),o=Zn(i);return t=yd(t),n.context===null?n.context=t:n.pendingContext=t,n=bn(l,o),n.payload={element:e},r=r===void 0?null:r,r!==null&&(n.callback=r),e=Xn(i,n,o),e!==null&&(dn(e,i,o,l),Mi(e,i,o)),o}function yl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function sc(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var t=e.retryLane;e.retryLane=t!==0&&t<n?t:n}}function ba(e,n){sc(e,n),(e=e.alternate)&&sc(e,n)}function ly(){return null}var kd=typeof reportError=="function"?reportError:function(e){console.error(e)};function La(e){this._internalRoot=e}jl.prototype.render=La.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(I(409));Dl(e,n,null,null)};jl.prototype.unmount=La.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;vt(function(){Dl(null,e,null,null)}),n[Rn]=null}};function jl(e){this._internalRoot=e}jl.prototype.unstable_scheduleHydration=function(e){if(e){var n=Kf();e={blockedOn:null,target:e,priority:n};for(var t=0;t<$n.length&&n!==0&&n<$n[t].priority;t++);$n.splice(t,0,e),t===0&&Gf(e)}};function Ra(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Nl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function cc(){}function oy(e,n,t,r,i){if(i){if(typeof r=="function"){var l=r;r=function(){var s=yl(o);l.call(s)}}var o=xd(n,r,e,0,null,!1,!1,"",cc);return e._reactRootContainer=o,e[Rn]=o.current,Wr(e.nodeType===8?e.parentNode:e),vt(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var u=r;r=function(){var s=yl(a);u.call(s)}}var a=_a(e,0,!1,null,null,!1,!1,"",cc);return e._reactRootContainer=a,e[Rn]=a.current,Wr(e.nodeType===8?e.parentNode:e),vt(function(){Dl(n,a,t,r)}),a}function Fl(e,n,t,r,i){var l=t._reactRootContainer;if(l){var o=l;if(typeof i=="function"){var u=i;i=function(){var a=yl(o);u.call(a)}}Dl(n,o,e,i)}else o=oy(t,n,e,i,r);return yl(o)}qf=function(e){switch(e.tag){case 3:var n=e.stateNode;if(n.current.memoizedState.isDehydrated){var t=Cr(n.pendingLanes);t!==0&&(Gu(n,t|1),Ue(n,de()),!(K&6)&&(tr=de()+500,it()))}break;case 13:vt(function(){var r=An(e,1);if(r!==null){var i=Ae();dn(r,e,1,i)}}),ba(e,1)}};Zu=function(e){if(e.tag===13){var n=An(e,134217728);if(n!==null){var t=Ae();dn(n,e,134217728,t)}ba(e,134217728)}};Yf=function(e){if(e.tag===13){var n=Zn(e),t=An(e,n);if(t!==null){var r=Ae();dn(t,e,n,r)}ba(e,n)}};Kf=function(){return X};Xf=function(e,n){var t=X;try{return X=e,n()}finally{X=t}};Yo=function(e,n,t){switch(n){case"input":if(Uo(e,t),n=t.name,t.type==="radio"&&n!=null){for(t=e;t.parentNode;)t=t.parentNode;for(t=t.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),n=0;n<t.length;n++){var r=t[n];if(r!==e&&r.form===e.form){var i=Pl(r);if(!i)throw Error(I(90));Pf(r),Uo(r,i)}}}break;case"textarea":_f(e,t);break;case"select":n=t.value,n!=null&&Ht(e,!!t.multiple,n,!1)}};Nf=Ta;Ff=vt;var uy={usingClientEntryPoint:!1,Events:[li,Nt,Pl,Df,jf,Ta]},xr={findFiberByHostInstance:ft,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},ay={bundleType:xr.bundleType,version:xr.version,rendererPackageName:xr.rendererPackageName,rendererConfig:xr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:jn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Bf(e),e===null?null:e.stateNode},findFiberByHostInstance:xr.findFiberByHostInstance||ly,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ii=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ii.isDisabled&&Ii.supportsFiber)try{Cl=Ii.inject(ay),wn=Ii}catch{}}Xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=uy;Xe.createPortal=function(e,n){var t=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ra(n))throw Error(I(200));return iy(e,n,null,t)};Xe.createRoot=function(e,n){if(!Ra(e))throw Error(I(299));var t=!1,r="",i=kd;return n!=null&&(n.unstable_strictMode===!0&&(t=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),n=_a(e,1,!1,null,null,t,!1,r,i),e[Rn]=n.current,Wr(e.nodeType===8?e.parentNode:e),new La(n)};Xe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(I(188)):(e=Object.keys(e).join(","),Error(I(268,e)));return e=Bf(n),e=e===null?null:e.stateNode,e};Xe.flushSync=function(e){return vt(e)};Xe.hydrate=function(e,n,t){if(!Nl(n))throw Error(I(200));return Fl(null,e,n,!0,t)};Xe.hydrateRoot=function(e,n,t){if(!Ra(e))throw Error(I(405));var r=t!=null&&t.hydratedSources||null,i=!1,l="",o=kd;if(t!=null&&(t.unstable_strictMode===!0&&(i=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),n=xd(n,null,e,1,t??null,i,!1,l,o),e[Rn]=n.current,Wr(e),r)for(e=0;e<r.length;e++)t=r[e],i=t._getVersion,i=i(t._source),n.mutableSourceEagerHydrationData==null?n.mutableSourceEagerHydrationData=[t,i]:n.mutableSourceEagerHydrationData.push(t,i);return new jl(n)};Xe.render=function(e,n,t){if(!Nl(n))throw Error(I(200));return Fl(null,e,n,!1,t)};Xe.unmountComponentAtNode=function(e){if(!Nl(e))throw Error(I(40));return e._reactRootContainer?(vt(function(){Fl(null,null,e,!1,function(){e._reactRootContainer=null,e[Rn]=null})}),!0):!1};Xe.unstable_batchedUpdates=Ta;Xe.unstable_renderSubtreeIntoContainer=function(e,n,t,r){if(!Nl(t))throw Error(I(200));if(e==null||e._reactInternals===void 0)throw Error(I(38));return Fl(e,n,t,!1,r)};Xe.version="18.2.0-next-9e3b772b8-20220608";(function(e){function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(t){console.error(t)}}n(),e.exports=Xe})(lm);var vd,fc=Ao;vd=fc.createRoot,fc.hydrateRoot;function sy(e,n){const t=n||{};return(e[e.length-1]===""?[...e,""]:e).join((t.padRight?" ":"")+","+(t.padLeft===!1?"":" ")).trim()}const cy=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,fy=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,py={};function pc(e,n){return((n||py).jsx?fy:cy).test(e)}const dy=/[ \t\n\f\r]/g;function hy(e){return typeof e=="object"?e.type==="text"?dc(e.value):!1:dc(e)}function dc(e){return e.replace(dy,"")===""}class ui{constructor(n,t,r){this.normal=t,this.property=n,r&&(this.space=r)}}ui.prototype.normal={};ui.prototype.property={};ui.prototype.space=void 0;function wd(e,n){const t={},r={};for(const i of e)Object.assign(t,i.property),Object.assign(r,i.normal);return new ui(t,r,n)}function Pu(e){return e.toLowerCase()}class $e{constructor(n,t){this.attribute=t,this.property=n}}$e.prototype.attribute="";$e.prototype.booleanish=!1;$e.prototype.boolean=!1;$e.prototype.commaOrSpaceSeparated=!1;$e.prototype.commaSeparated=!1;$e.prototype.defined=!1;$e.prototype.mustUseProperty=!1;$e.prototype.number=!1;$e.prototype.overloadedBoolean=!1;$e.prototype.property="";$e.prototype.spaceSeparated=!1;$e.prototype.space=void 0;let my=0;const U=Et(),me=Et(),Iu=Et(),b=Et(),ne=Et(),Kt=Et(),Ve=Et();function Et(){return 2**++my}const _u=Object.freeze(Object.defineProperty({__proto__:null,boolean:U,booleanish:me,commaOrSpaceSeparated:Ve,commaSeparated:Kt,number:b,overloadedBoolean:Iu,spaceSeparated:ne},Symbol.toStringTag,{value:"Module"})),yo=Object.keys(_u);class Aa extends $e{constructor(n,t,r,i){let l=-1;if(super(n,t),hc(this,"space",i),typeof r=="number")for(;++l<yo.length;){const o=yo[l];hc(this,yo[l],(r&_u[o])===_u[o])}}}Aa.prototype.defined=!0;function hc(e,n,t){t&&(e[n]=t)}function ur(e){const n={},t={};for(const[r,i]of Object.entries(e.properties)){const l=new Aa(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),n[r]=l,t[Pu(r)]=r,t[Pu(l.attribute)]=r}return new ui(n,t,e.space)}const Sd=ur({properties:{ariaActiveDescendant:null,ariaAtomic:me,ariaAutoComplete:null,ariaBusy:me,ariaChecked:me,ariaColCount:b,ariaColIndex:b,ariaColSpan:b,ariaControls:ne,ariaCurrent:null,ariaDescribedBy:ne,ariaDetails:null,ariaDisabled:me,ariaDropEffect:ne,ariaErrorMessage:null,ariaExpanded:me,ariaFlowTo:ne,ariaGrabbed:me,ariaHasPopup:null,ariaHidden:me,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:ne,ariaLevel:b,ariaLive:null,ariaModal:me,ariaMultiLine:me,ariaMultiSelectable:me,ariaOrientation:null,ariaOwns:ne,ariaPlaceholder:null,ariaPosInSet:b,ariaPressed:me,ariaReadOnly:me,ariaRelevant:null,ariaRequired:me,ariaRoleDescription:ne,ariaRowCount:b,ariaRowIndex:b,ariaRowSpan:b,ariaSelected:me,ariaSetSize:b,ariaSort:null,ariaValueMax:b,ariaValueMin:b,ariaValueNow:b,ariaValueText:null,role:null},transform(e,n){return n==="role"?n:"aria-"+n.slice(4).toLowerCase()}});function Cd(e,n){return n in e?e[n]:n}function Ed(e,n){return Cd(e,n.toLowerCase())}const gy=ur({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Kt,acceptCharset:ne,accessKey:ne,action:null,allow:null,allowFullScreen:U,allowPaymentRequest:U,allowUserMedia:U,alt:null,as:null,async:U,autoCapitalize:null,autoComplete:ne,autoFocus:U,autoPlay:U,blocking:ne,capture:null,charSet:null,checked:U,cite:null,className:ne,cols:b,colSpan:null,content:null,contentEditable:me,controls:U,controlsList:ne,coords:b|Kt,crossOrigin:null,data:null,dateTime:null,decoding:null,default:U,defer:U,dir:null,dirName:null,disabled:U,download:Iu,draggable:me,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:U,formTarget:null,headers:ne,height:b,hidden:Iu,high:b,href:null,hrefLang:null,htmlFor:ne,httpEquiv:ne,id:null,imageSizes:null,imageSrcSet:null,inert:U,inputMode:null,integrity:null,is:null,isMap:U,itemId:null,itemProp:ne,itemRef:ne,itemScope:U,itemType:ne,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:U,low:b,manifest:null,max:null,maxLength:b,media:null,method:null,min:null,minLength:b,multiple:U,muted:U,name:null,nonce:null,noModule:U,noValidate:U,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:U,optimum:b,pattern:null,ping:ne,placeholder:null,playsInline:U,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:U,referrerPolicy:null,rel:ne,required:U,reversed:U,rows:b,rowSpan:b,sandbox:ne,scope:null,scoped:U,seamless:U,selected:U,shadowRootClonable:U,shadowRootDelegatesFocus:U,shadowRootMode:null,shape:null,size:b,sizes:null,slot:null,span:b,spellCheck:me,src:null,srcDoc:null,srcLang:null,srcSet:null,start:b,step:null,style:null,tabIndex:b,target:null,title:null,translate:null,type:null,typeMustMatch:U,useMap:null,value:me,width:b,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:ne,axis:null,background:null,bgColor:null,border:b,borderColor:null,bottomMargin:b,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:U,declare:U,event:null,face:null,frame:null,frameBorder:null,hSpace:b,leftMargin:b,link:null,longDesc:null,lowSrc:null,marginHeight:b,marginWidth:b,noResize:U,noHref:U,noShade:U,noWrap:U,object:null,profile:null,prompt:null,rev:null,rightMargin:b,rules:null,scheme:null,scrolling:me,standby:null,summary:null,text:null,topMargin:b,valueType:null,version:null,vAlign:null,vLink:null,vSpace:b,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:U,disableRemotePlayback:U,prefix:null,property:null,results:b,security:null,unselectable:null},space:"html",transform:Ed}),yy=ur({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Ve,accentHeight:b,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:b,amplitude:b,arabicForm:null,ascent:b,attributeName:null,attributeType:null,azimuth:b,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:b,by:null,calcMode:null,capHeight:b,className:ne,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:b,diffuseConstant:b,direction:null,display:null,dur:null,divisor:b,dominantBaseline:null,download:U,dx:null,dy:null,edgeMode:null,editable:null,elevation:b,enableBackground:null,end:null,event:null,exponent:b,externalResourcesRequired:null,fill:null,fillOpacity:b,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Kt,g2:Kt,glyphName:Kt,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:b,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:b,horizOriginX:b,horizOriginY:b,id:null,ideographic:b,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:b,k:b,k1:b,k2:b,k3:b,k4:b,kernelMatrix:Ve,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:b,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:b,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:b,overlineThickness:b,paintOrder:null,panose1:null,path:null,pathLength:b,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:ne,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:b,pointsAtY:b,pointsAtZ:b,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Ve,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Ve,rev:Ve,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Ve,requiredFeatures:Ve,requiredFonts:Ve,requiredFormats:Ve,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:b,specularExponent:b,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:b,strikethroughThickness:b,string:null,stroke:null,strokeDashArray:Ve,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:b,strokeOpacity:b,strokeWidth:null,style:null,surfaceScale:b,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Ve,tabIndex:b,tableValues:null,target:null,targetX:b,targetY:b,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Ve,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:b,underlineThickness:b,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:b,values:null,vAlphabetic:b,vMathematical:b,vectorEffect:null,vHanging:b,vIdeographic:b,version:null,vertAdvY:b,vertOriginX:b,vertOriginY:b,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:b,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Cd}),Td=ur({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,n){return"xlink:"+n.slice(5).toLowerCase()}}),zd=ur({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Ed}),Pd=ur({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,n){return"xml:"+n.slice(3).toLowerCase()}}),xy={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},ky=/[A-Z]/g,mc=/-[a-z]/g,vy=/^data[-\w.:]+$/i;function wy(e,n){const t=Pu(n);let r=n,i=$e;if(t in e.normal)return e.property[e.normal[t]];if(t.length>4&&t.slice(0,4)==="data"&&vy.test(n)){if(n.charAt(4)==="-"){const l=n.slice(5).replace(mc,Cy);r="data"+l.charAt(0).toUpperCase()+l.slice(1)}else{const l=n.slice(4);if(!mc.test(l)){let o=l.replace(ky,Sy);o.charAt(0)!=="-"&&(o="-"+o),n="data"+o}}i=Aa}return new i(r,n)}function Sy(e){return"-"+e.toLowerCase()}function Cy(e){return e.charAt(1).toUpperCase()}const Ey=wd([Sd,gy,Td,zd,Pd],"html"),Da=wd([Sd,yy,Td,zd,Pd],"svg");function Ty(e){return e.join(" ").trim()}var ja={},gc=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,zy=/\n/g,Py=/^\s*/,Iy=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,_y=/^:\s*/,by=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,Ly=/^[;\s]*/,Ry=/^\s+|\s+$/g,Ay=`
`,yc="/",xc="*",ct="",Dy="comment",jy="declaration",Ny=function(e,n){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];n=n||{};var t=1,r=1;function i(g){var v=g.match(zy);v&&(t+=v.length);var E=g.lastIndexOf(Ay);r=~E?g.length-E:r+g.length}function l(){var g={line:t,column:r};return function(v){return v.position=new o(g),s(),v}}function o(g){this.start=g,this.end={line:t,column:r},this.source=n.source}o.prototype.content=e;function u(g){var v=new Error(n.source+":"+t+":"+r+": "+g);if(v.reason=g,v.filename=n.source,v.line=t,v.column=r,v.source=e,!n.silent)throw v}function a(g){var v=g.exec(e);if(v){var E=v[0];return i(E),e=e.slice(E.length),v}}function s(){a(Py)}function f(g){var v;for(g=g||[];v=c();)v!==!1&&g.push(v);return g}function c(){var g=l();if(!(yc!=e.charAt(0)||xc!=e.charAt(1))){for(var v=2;ct!=e.charAt(v)&&(xc!=e.charAt(v)||yc!=e.charAt(v+1));)++v;if(v+=2,ct===e.charAt(v-1))return u("End of comment missing");var E=e.slice(2,v-2);return r+=2,i(E),e=e.slice(v),r+=2,g({type:Dy,comment:E})}}function p(){var g=l(),v=a(Iy);if(v){if(c(),!a(_y))return u("property missing ':'");var E=a(by),h=g({type:jy,property:kc(v[0].replace(gc,ct)),value:E?kc(E[0].replace(gc,ct)):ct});return a(Ly),h}}function d(){var g=[];f(g);for(var v;v=p();)v!==!1&&(g.push(v),f(g));return g}return s(),d()};function kc(e){return e?e.replace(Ry,ct):ct}var Fy=qi&&qi.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ja,"__esModule",{value:!0});ja.default=Oy;var My=Fy(Ny);function Oy(e,n){var t=null;if(!e||typeof e!="string")return t;var r=(0,My.default)(e),i=typeof n=="function";return r.forEach(function(l){if(l.type==="declaration"){var o=l.property,u=l.value;i?n(o,u,l):u&&(t=t||{},t[o]=u)}}),t}var Ml={};Object.defineProperty(Ml,"__esModule",{value:!0});Ml.camelCase=void 0;var By=/^--[a-zA-Z0-9_-]+$/,Uy=/-([a-z])/g,$y=/^[^-]+$/,Hy=/^-(webkit|moz|ms|o|khtml)-/,Vy=/^-(ms)-/,Wy=function(e){return!e||$y.test(e)||By.test(e)},Qy=function(e,n){return n.toUpperCase()},vc=function(e,n){return"".concat(n,"-")},qy=function(e,n){return n===void 0&&(n={}),Wy(e)?e:(e=e.toLowerCase(),n.reactCompat?e=e.replace(Vy,vc):e=e.replace(Hy,vc),e.replace(Uy,Qy))};Ml.camelCase=qy;var Yy=qi&&qi.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},Ky=Yy(ja),Xy=Ml;function bu(e,n){var t={};return!e||typeof e!="string"||(0,Ky.default)(e,function(r,i){r&&i&&(t[(0,Xy.camelCase)(r,n)]=i)}),t}bu.default=bu;var Gy=bu;const Id=_d("end"),Na=_d("start");function _d(e){return n;function n(t){const r=t&&t.position&&t.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function Zy(e){const n=Na(e),t=Id(e);if(n&&t)return{start:n,end:t}}function Dr(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?wc(e.position):"start"in e||"end"in e?wc(e):"line"in e||"column"in e?Lu(e):""}function Lu(e){return Sc(e&&e.line)+":"+Sc(e&&e.column)}function wc(e){return Lu(e&&e.start)+"-"+Lu(e&&e.end)}function Sc(e){return e&&typeof e=="number"?e:1}class be extends Error{constructor(n,t,r){super(),typeof t=="string"&&(r=t,t=void 0);let i="",l={},o=!1;if(t&&("line"in t&&"column"in t?l={place:t}:"start"in t&&"end"in t?l={place:t}:"type"in t?l={ancestors:[t],place:t.position}:l={...t}),typeof n=="string"?i=n:!l.cause&&n&&(o=!0,i=n.message,l.cause=n),!l.ruleId&&!l.source&&typeof r=="string"){const a=r.indexOf(":");a===-1?l.ruleId=r:(l.source=r.slice(0,a),l.ruleId=r.slice(a+1))}if(!l.place&&l.ancestors&&l.ancestors){const a=l.ancestors[l.ancestors.length-1];a&&(l.place=a.position)}const u=l.place&&"start"in l.place?l.place.start:l.place;this.ancestors=l.ancestors||void 0,this.cause=l.cause||void 0,this.column=u?u.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=u?u.line:void 0,this.name=Dr(l.place)||"1:1",this.place=l.place||void 0,this.reason=this.message,this.ruleId=l.ruleId||void 0,this.source=l.source||void 0,this.stack=o&&l.cause&&typeof l.cause.stack=="string"?l.cause.stack:"",this.actual,this.expected,this.note,this.url}}be.prototype.file="";be.prototype.name="";be.prototype.reason="";be.prototype.message="";be.prototype.stack="";be.prototype.column=void 0;be.prototype.line=void 0;be.prototype.ancestors=void 0;be.prototype.cause=void 0;be.prototype.fatal=void 0;be.prototype.place=void 0;be.prototype.ruleId=void 0;be.prototype.source=void 0;const Fa={}.hasOwnProperty,Jy=new Map,e1=/[A-Z]/g,n1=new Set(["table","tbody","thead","tfoot","tr"]),t1=new Set(["td","th"]),bd="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function r1(e,n){if(!n||n.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const t=n.filePath||void 0;let r;if(n.development){if(typeof n.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=f1(t,n.jsxDEV)}else{if(typeof n.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof n.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=c1(t,n.jsx,n.jsxs)}const i={Fragment:n.Fragment,ancestors:[],components:n.components||{},create:r,elementAttributeNameCase:n.elementAttributeNameCase||"react",evaluater:n.createEvaluater?n.createEvaluater():void 0,filePath:t,ignoreInvalidStyle:n.ignoreInvalidStyle||!1,passKeys:n.passKeys!==!1,passNode:n.passNode||!1,schema:n.space==="svg"?Da:Ey,stylePropertyNameCase:n.stylePropertyNameCase||"dom",tableCellAlignToStyle:n.tableCellAlignToStyle!==!1},l=Ld(i,e,void 0);return l&&typeof l!="string"?l:i.create(e,i.Fragment,{children:l||void 0},void 0)}function Ld(e,n,t){if(n.type==="element")return i1(e,n,t);if(n.type==="mdxFlowExpression"||n.type==="mdxTextExpression")return l1(e,n);if(n.type==="mdxJsxFlowElement"||n.type==="mdxJsxTextElement")return u1(e,n,t);if(n.type==="mdxjsEsm")return o1(e,n);if(n.type==="root")return a1(e,n,t);if(n.type==="text")return s1(e,n)}function i1(e,n,t){const r=e.schema;let i=r;n.tagName.toLowerCase()==="svg"&&r.space==="html"&&(i=Da,e.schema=i),e.ancestors.push(n);const l=Ad(e,n.tagName,!1),o=p1(e,n);let u=Oa(e,n);return n1.has(n.tagName)&&(u=u.filter(function(a){return typeof a=="string"?!hy(a):!0})),Rd(e,o,l,n),Ma(o,u),e.ancestors.pop(),e.schema=r,e.create(n,l,o,t)}function l1(e,n){if(n.data&&n.data.estree&&e.evaluater){const r=n.data.estree.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}ei(e,n.position)}function o1(e,n){if(n.data&&n.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(n.data.estree);ei(e,n.position)}function u1(e,n,t){const r=e.schema;let i=r;n.name==="svg"&&r.space==="html"&&(i=Da,e.schema=i),e.ancestors.push(n);const l=n.name===null?e.Fragment:Ad(e,n.name,!0),o=d1(e,n),u=Oa(e,n);return Rd(e,o,l,n),Ma(o,u),e.ancestors.pop(),e.schema=r,e.create(n,l,o,t)}function a1(e,n,t){const r={};return Ma(r,Oa(e,n)),e.create(n,e.Fragment,r,t)}function s1(e,n){return n.value}function Rd(e,n,t,r){typeof t!="string"&&t!==e.Fragment&&e.passNode&&(n.node=r)}function Ma(e,n){if(n.length>0){const t=n.length>1?n:n[0];t&&(e.children=t)}}function c1(e,n,t){return r;function r(i,l,o,u){const s=Array.isArray(o.children)?t:n;return u?s(l,o,u):s(l,o)}}function f1(e,n){return t;function t(r,i,l,o){const u=Array.isArray(l.children),a=Na(r);return n(i,l,o,u,{columnNumber:a?a.column-1:void 0,fileName:e,lineNumber:a?a.line:void 0},void 0)}}function p1(e,n){const t={};let r,i;for(i in n.properties)if(i!=="children"&&Fa.call(n.properties,i)){const l=h1(e,i,n.properties[i]);if(l){const[o,u]=l;e.tableCellAlignToStyle&&o==="align"&&typeof u=="string"&&t1.has(n.tagName)?r=u:t[o]=u}}if(r){const l=t.style||(t.style={});l[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return t}function d1(e,n){const t={};for(const r of n.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const l=r.data.estree.body[0];l.type;const o=l.expression;o.type;const u=o.properties[0];u.type,Object.assign(t,e.evaluater.evaluateExpression(u.argument))}else ei(e,n.position);else{const i=r.name;let l;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const u=r.value.data.estree.body[0];u.type,l=e.evaluater.evaluateExpression(u.expression)}else ei(e,n.position);else l=r.value===null?!0:r.value;t[i]=l}return t}function Oa(e,n){const t=[];let r=-1;const i=e.passKeys?new Map:Jy;for(;++r<n.children.length;){const l=n.children[r];let o;if(e.passKeys){const a=l.type==="element"?l.tagName:l.type==="mdxJsxFlowElement"||l.type==="mdxJsxTextElement"?l.name:void 0;if(a){const s=i.get(a)||0;o=a+"-"+s,i.set(a,s+1)}}const u=Ld(e,l,o);u!==void 0&&t.push(u)}return t}function h1(e,n,t){const r=wy(e.schema,n);if(!(t==null||typeof t=="number"&&Number.isNaN(t))){if(Array.isArray(t)&&(t=r.commaSeparated?sy(t):Ty(t)),r.property==="style"){let i=typeof t=="object"?t:m1(e,String(t));return e.stylePropertyNameCase==="css"&&(i=g1(i)),["style",i]}return[e.elementAttributeNameCase==="react"&&r.space?xy[r.property]||r.property:r.attribute,t]}}function m1(e,n){try{return Gy(n,{reactCompat:!0})}catch(t){if(e.ignoreInvalidStyle)return{};const r=t,i=new be("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:r,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=e.filePath||void 0,i.url=bd+"#cannot-parse-style-attribute",i}}function Ad(e,n,t){let r;if(!t)r={type:"Literal",value:n};else if(n.includes(".")){const i=n.split(".");let l=-1,o;for(;++l<i.length;){const u=pc(i[l])?{type:"Identifier",name:i[l]}:{type:"Literal",value:i[l]};o=o?{type:"MemberExpression",object:o,property:u,computed:!!(l&&u.type==="Literal"),optional:!1}:u}r=o}else r=pc(n)&&!/^[a-z]/.test(n)?{type:"Identifier",name:n}:{type:"Literal",value:n};if(r.type==="Literal"){const i=r.value;return Fa.call(e.components,i)?e.components[i]:i}if(e.evaluater)return e.evaluater.evaluateExpression(r);ei(e)}function ei(e,n){const t=new be("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:n,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=bd+"#cannot-handle-mdx-estrees-without-createevaluater",t}function g1(e){const n={};let t;for(t in e)Fa.call(e,t)&&(n[y1(t)]=e[t]);return n}function y1(e){let n=e.replace(e1,x1);return n.slice(0,3)==="ms-"&&(n="-"+n),n}function x1(e){return"-"+e.toLowerCase()}const xo={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},k1={};function Ba(e,n){const t=n||k1,r=typeof t.includeImageAlt=="boolean"?t.includeImageAlt:!0,i=typeof t.includeHtml=="boolean"?t.includeHtml:!0;return Dd(e,r,i)}function Dd(e,n,t){if(v1(e)){if("value"in e)return e.type==="html"&&!t?"":e.value;if(n&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Cc(e.children,n,t)}return Array.isArray(e)?Cc(e,n,t):""}function Cc(e,n,t){const r=[];let i=-1;for(;++i<e.length;)r[i]=Dd(e[i],n,t);return r.join("")}function v1(e){return!!(e&&typeof e=="object")}const Ec=document.createElement("i");function Ua(e){const n="&"+e+";";Ec.innerHTML=n;const t=Ec.textContent;return t.charCodeAt(t.length-1)===59&&e!=="semi"||t===n?!1:t}function Ye(e,n,t,r){const i=e.length;let l=0,o;if(n<0?n=-n>i?0:i+n:n=n>i?i:n,t=t>0?t:0,r.length<1e4)o=Array.from(r),o.unshift(n,t),e.splice(...o);else for(t&&e.splice(n,t);l<r.length;)o=r.slice(l,l+1e4),o.unshift(n,0),e.splice(...o),l+=1e4,n+=1e4}function en(e,n){return e.length>0?(Ye(e,e.length,0,n),e):n}const Tc={}.hasOwnProperty;function jd(e){const n={};let t=-1;for(;++t<e.length;)w1(n,e[t]);return n}function w1(e,n){let t;for(t in n){const i=(Tc.call(e,t)?e[t]:void 0)||(e[t]={}),l=n[t];let o;if(l)for(o in l){Tc.call(i,o)||(i[o]=[]);const u=l[o];S1(i[o],Array.isArray(u)?u:u?[u]:[])}}}function S1(e,n){let t=-1;const r=[];for(;++t<n.length;)(n[t].add==="after"?e:r).push(n[t]);Ye(e,0,0,r)}function Nd(e,n){const t=Number.parseInt(e,n);return t<9||t===11||t>13&&t<32||t>126&&t<160||t>55295&&t<57344||t>64975&&t<65008||(t&65535)===65535||(t&65535)===65534||t>1114111?"�":String.fromCodePoint(t)}function hn(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Re=lt(/[A-Za-z]/),Ie=lt(/[\dA-Za-z]/),C1=lt(/[#-'*+\--9=?A-Z^-~]/);function xl(e){return e!==null&&(e<32||e===127)}const Ru=lt(/\d/),E1=lt(/[\dA-Fa-f]/),T1=lt(/[!-/:-@[-`{-~]/);function F(e){return e!==null&&e<-2}function J(e){return e!==null&&(e<0||e===32)}function $(e){return e===-2||e===-1||e===32}const Ol=lt(/\p{P}|\p{S}/u),wt=lt(/\s/);function lt(e){return n;function n(t){return t!==null&&t>-1&&e.test(String.fromCharCode(t))}}function ar(e){const n=[];let t=-1,r=0,i=0;for(;++t<e.length;){const l=e.charCodeAt(t);let o="";if(l===37&&Ie(e.charCodeAt(t+1))&&Ie(e.charCodeAt(t+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(o=String.fromCharCode(l));else if(l>55295&&l<57344){const u=e.charCodeAt(t+1);l<56320&&u>56319&&u<57344?(o=String.fromCharCode(l,u),i=1):o="�"}else o=String.fromCharCode(l);o&&(n.push(e.slice(r,t),encodeURIComponent(o)),r=t+i+1,o=""),i&&(t+=i,i=0)}return n.join("")+e.slice(r)}function Q(e,n,t,r){const i=r?r-1:Number.POSITIVE_INFINITY;let l=0;return o;function o(a){return $(a)?(e.enter(t),u(a)):n(a)}function u(a){return $(a)&&l++<i?(e.consume(a),u):(e.exit(t),n(a))}}const z1={tokenize:P1};function P1(e){const n=e.attempt(this.parser.constructs.contentInitial,r,i);let t;return n;function r(u){if(u===null){e.consume(u);return}return e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),Q(e,n,"linePrefix")}function i(u){return e.enter("paragraph"),l(u)}function l(u){const a=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=a),t=a,o(u)}function o(u){if(u===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(u);return}return F(u)?(e.consume(u),e.exit("chunkText"),l):(e.consume(u),o)}}const I1={tokenize:_1},zc={tokenize:b1};function _1(e){const n=this,t=[];let r=0,i,l,o;return u;function u(y){if(r<t.length){const T=t[r];return n.containerState=T[1],e.attempt(T[0].continuation,a,s)(y)}return s(y)}function a(y){if(r++,n.containerState._closeFlow){n.containerState._closeFlow=void 0,i&&m();const T=n.events.length;let P=T,S;for(;P--;)if(n.events[P][0]==="exit"&&n.events[P][1].type==="chunkFlow"){S=n.events[P][1].end;break}h(r);let _=T;for(;_<n.events.length;)n.events[_][1].end={...S},_++;return Ye(n.events,P+1,0,n.events.slice(T)),n.events.length=_,s(y)}return u(y)}function s(y){if(r===t.length){if(!i)return p(y);if(i.currentConstruct&&i.currentConstruct.concrete)return g(y);n.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return n.containerState={},e.check(zc,f,c)(y)}function f(y){return i&&m(),h(r),p(y)}function c(y){return n.parser.lazy[n.now().line]=r!==t.length,o=n.now().offset,g(y)}function p(y){return n.containerState={},e.attempt(zc,d,g)(y)}function d(y){return r++,t.push([n.currentConstruct,n.containerState]),p(y)}function g(y){if(y===null){i&&m(),h(0),e.consume(y);return}return i=i||n.parser.flow(n.now()),e.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:l}),v(y)}function v(y){if(y===null){E(e.exit("chunkFlow"),!0),h(0),e.consume(y);return}return F(y)?(e.consume(y),E(e.exit("chunkFlow")),r=0,n.interrupt=void 0,u):(e.consume(y),v)}function E(y,T){const P=n.sliceStream(y);if(T&&P.push(null),y.previous=l,l&&(l.next=y),l=y,i.defineSkip(y.start),i.write(P),n.parser.lazy[y.start.line]){let S=i.events.length;for(;S--;)if(i.events[S][1].start.offset<o&&(!i.events[S][1].end||i.events[S][1].end.offset>o))return;const _=n.events.length;let L=_,M,C;for(;L--;)if(n.events[L][0]==="exit"&&n.events[L][1].type==="chunkFlow"){if(M){C=n.events[L][1].end;break}M=!0}for(h(r),S=_;S<n.events.length;)n.events[S][1].end={...C},S++;Ye(n.events,L+1,0,n.events.slice(_)),n.events.length=S}}function h(y){let T=t.length;for(;T-- >y;){const P=t[T];n.containerState=P[1],P[0].exit.call(n,e)}t.length=y}function m(){i.write([null]),l=void 0,i=void 0,n.containerState._closeFlow=void 0}}function b1(e,n,t){return Q(e,e.attempt(this.parser.constructs.document,n,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function rr(e){if(e===null||J(e)||wt(e))return 1;if(Ol(e))return 2}function Bl(e,n,t){const r=[];let i=-1;for(;++i<e.length;){const l=e[i].resolveAll;l&&!r.includes(l)&&(n=l(n,t),r.push(l))}return n}const Au={name:"attention",resolveAll:L1,tokenize:R1};function L1(e,n){let t=-1,r,i,l,o,u,a,s,f;for(;++t<e.length;)if(e[t][0]==="enter"&&e[t][1].type==="attentionSequence"&&e[t][1]._close){for(r=t;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&n.sliceSerialize(e[r][1]).charCodeAt(0)===n.sliceSerialize(e[t][1]).charCodeAt(0)){if((e[r][1]._close||e[t][1]._open)&&(e[t][1].end.offset-e[t][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[t][1].end.offset-e[t][1].start.offset)%3))continue;a=e[r][1].end.offset-e[r][1].start.offset>1&&e[t][1].end.offset-e[t][1].start.offset>1?2:1;const c={...e[r][1].end},p={...e[t][1].start};Pc(c,-a),Pc(p,a),o={type:a>1?"strongSequence":"emphasisSequence",start:c,end:{...e[r][1].end}},u={type:a>1?"strongSequence":"emphasisSequence",start:{...e[t][1].start},end:p},l={type:a>1?"strongText":"emphasisText",start:{...e[r][1].end},end:{...e[t][1].start}},i={type:a>1?"strong":"emphasis",start:{...o.start},end:{...u.end}},e[r][1].end={...o.start},e[t][1].start={...u.end},s=[],e[r][1].end.offset-e[r][1].start.offset&&(s=en(s,[["enter",e[r][1],n],["exit",e[r][1],n]])),s=en(s,[["enter",i,n],["enter",o,n],["exit",o,n],["enter",l,n]]),s=en(s,Bl(n.parser.constructs.insideSpan.null,e.slice(r+1,t),n)),s=en(s,[["exit",l,n],["enter",u,n],["exit",u,n],["exit",i,n]]),e[t][1].end.offset-e[t][1].start.offset?(f=2,s=en(s,[["enter",e[t][1],n],["exit",e[t][1],n]])):f=0,Ye(e,r-1,t-r+3,s),t=r+s.length-f-2;break}}for(t=-1;++t<e.length;)e[t][1].type==="attentionSequence"&&(e[t][1].type="data");return e}function R1(e,n){const t=this.parser.constructs.attentionMarkers.null,r=this.previous,i=rr(r);let l;return o;function o(a){return l=a,e.enter("attentionSequence"),u(a)}function u(a){if(a===l)return e.consume(a),u;const s=e.exit("attentionSequence"),f=rr(a),c=!f||f===2&&i||t.includes(a),p=!i||i===2&&f||t.includes(r);return s._open=!!(l===42?c:c&&(i||!p)),s._close=!!(l===42?p:p&&(f||!c)),n(a)}}function Pc(e,n){e.column+=n,e.offset+=n,e._bufferIndex+=n}const A1={name:"autolink",tokenize:D1};function D1(e,n,t){let r=0;return i;function i(d){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),l}function l(d){return Re(d)?(e.consume(d),o):d===64?t(d):s(d)}function o(d){return d===43||d===45||d===46||Ie(d)?(r=1,u(d)):s(d)}function u(d){return d===58?(e.consume(d),r=0,a):(d===43||d===45||d===46||Ie(d))&&r++<32?(e.consume(d),u):(r=0,s(d))}function a(d){return d===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.exit("autolink"),n):d===null||d===32||d===60||xl(d)?t(d):(e.consume(d),a)}function s(d){return d===64?(e.consume(d),f):C1(d)?(e.consume(d),s):t(d)}function f(d){return Ie(d)?c(d):t(d)}function c(d){return d===46?(e.consume(d),r=0,f):d===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.exit("autolink"),n):p(d)}function p(d){if((d===45||Ie(d))&&r++<63){const g=d===45?p:c;return e.consume(d),g}return t(d)}}const ai={partial:!0,tokenize:j1};function j1(e,n,t){return r;function r(l){return $(l)?Q(e,i,"linePrefix")(l):i(l)}function i(l){return l===null||F(l)?n(l):t(l)}}const Fd={continuation:{tokenize:F1},exit:M1,name:"blockQuote",tokenize:N1};function N1(e,n,t){const r=this;return i;function i(o){if(o===62){const u=r.containerState;return u.open||(e.enter("blockQuote",{_container:!0}),u.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(o),e.exit("blockQuoteMarker"),l}return t(o)}function l(o){return $(o)?(e.enter("blockQuotePrefixWhitespace"),e.consume(o),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),n):(e.exit("blockQuotePrefix"),n(o))}}function F1(e,n,t){const r=this;return i;function i(o){return $(o)?Q(e,l,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o):l(o)}function l(o){return e.attempt(Fd,n,t)(o)}}function M1(e){e.exit("blockQuote")}const Md={name:"characterEscape",tokenize:O1};function O1(e,n,t){return r;function r(l){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(l),e.exit("escapeMarker"),i}function i(l){return T1(l)?(e.enter("characterEscapeValue"),e.consume(l),e.exit("characterEscapeValue"),e.exit("characterEscape"),n):t(l)}}const Od={name:"characterReference",tokenize:B1};function B1(e,n,t){const r=this;let i=0,l,o;return u;function u(c){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(c),e.exit("characterReferenceMarker"),a}function a(c){return c===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(c),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),l=31,o=Ie,f(c))}function s(c){return c===88||c===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(c),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),l=6,o=E1,f):(e.enter("characterReferenceValue"),l=7,o=Ru,f(c))}function f(c){if(c===59&&i){const p=e.exit("characterReferenceValue");return o===Ie&&!Ua(r.sliceSerialize(p))?t(c):(e.enter("characterReferenceMarker"),e.consume(c),e.exit("characterReferenceMarker"),e.exit("characterReference"),n)}return o(c)&&i++<l?(e.consume(c),f):t(c)}}const Ic={partial:!0,tokenize:$1},_c={concrete:!0,name:"codeFenced",tokenize:U1};function U1(e,n,t){const r=this,i={partial:!0,tokenize:P};let l=0,o=0,u;return a;function a(S){return s(S)}function s(S){const _=r.events[r.events.length-1];return l=_&&_[1].type==="linePrefix"?_[2].sliceSerialize(_[1],!0).length:0,u=S,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),f(S)}function f(S){return S===u?(o++,e.consume(S),f):o<3?t(S):(e.exit("codeFencedFenceSequence"),$(S)?Q(e,c,"whitespace")(S):c(S))}function c(S){return S===null||F(S)?(e.exit("codeFencedFence"),r.interrupt?n(S):e.check(Ic,v,T)(S)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),p(S))}function p(S){return S===null||F(S)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),c(S)):$(S)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),Q(e,d,"whitespace")(S)):S===96&&S===u?t(S):(e.consume(S),p)}function d(S){return S===null||F(S)?c(S):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),g(S))}function g(S){return S===null||F(S)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),c(S)):S===96&&S===u?t(S):(e.consume(S),g)}function v(S){return e.attempt(i,T,E)(S)}function E(S){return e.enter("lineEnding"),e.consume(S),e.exit("lineEnding"),h}function h(S){return l>0&&$(S)?Q(e,m,"linePrefix",l+1)(S):m(S)}function m(S){return S===null||F(S)?e.check(Ic,v,T)(S):(e.enter("codeFlowValue"),y(S))}function y(S){return S===null||F(S)?(e.exit("codeFlowValue"),m(S)):(e.consume(S),y)}function T(S){return e.exit("codeFenced"),n(S)}function P(S,_,L){let M=0;return C;function C(V){return S.enter("lineEnding"),S.consume(V),S.exit("lineEnding"),D}function D(V){return S.enter("codeFencedFence"),$(V)?Q(S,N,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(V):N(V)}function N(V){return V===u?(S.enter("codeFencedFenceSequence"),q(V)):L(V)}function q(V){return V===u?(M++,S.consume(V),q):M>=o?(S.exit("codeFencedFenceSequence"),$(V)?Q(S,ee,"whitespace")(V):ee(V)):L(V)}function ee(V){return V===null||F(V)?(S.exit("codeFencedFence"),_(V)):L(V)}}}function $1(e,n,t){const r=this;return i;function i(o){return o===null?t(o):(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),l)}function l(o){return r.parser.lazy[r.now().line]?t(o):n(o)}}const ko={name:"codeIndented",tokenize:V1},H1={partial:!0,tokenize:W1};function V1(e,n,t){const r=this;return i;function i(s){return e.enter("codeIndented"),Q(e,l,"linePrefix",4+1)(s)}function l(s){const f=r.events[r.events.length-1];return f&&f[1].type==="linePrefix"&&f[2].sliceSerialize(f[1],!0).length>=4?o(s):t(s)}function o(s){return s===null?a(s):F(s)?e.attempt(H1,o,a)(s):(e.enter("codeFlowValue"),u(s))}function u(s){return s===null||F(s)?(e.exit("codeFlowValue"),o(s)):(e.consume(s),u)}function a(s){return e.exit("codeIndented"),n(s)}}function W1(e,n,t){const r=this;return i;function i(o){return r.parser.lazy[r.now().line]?t(o):F(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),i):Q(e,l,"linePrefix",4+1)(o)}function l(o){const u=r.events[r.events.length-1];return u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?n(o):F(o)?i(o):t(o)}}const Q1={name:"codeText",previous:Y1,resolve:q1,tokenize:K1};function q1(e){let n=e.length-4,t=3,r,i;if((e[t][1].type==="lineEnding"||e[t][1].type==="space")&&(e[n][1].type==="lineEnding"||e[n][1].type==="space")){for(r=t;++r<n;)if(e[r][1].type==="codeTextData"){e[t][1].type="codeTextPadding",e[n][1].type="codeTextPadding",t+=2,n-=2;break}}for(r=t-1,n++;++r<=n;)i===void 0?r!==n&&e[r][1].type!=="lineEnding"&&(i=r):(r===n||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),n-=r-i-2,r=i+2),i=void 0);return e}function Y1(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function K1(e,n,t){let r=0,i,l;return o;function o(c){return e.enter("codeText"),e.enter("codeTextSequence"),u(c)}function u(c){return c===96?(e.consume(c),r++,u):(e.exit("codeTextSequence"),a(c))}function a(c){return c===null?t(c):c===32?(e.enter("space"),e.consume(c),e.exit("space"),a):c===96?(l=e.enter("codeTextSequence"),i=0,f(c)):F(c)?(e.enter("lineEnding"),e.consume(c),e.exit("lineEnding"),a):(e.enter("codeTextData"),s(c))}function s(c){return c===null||c===32||c===96||F(c)?(e.exit("codeTextData"),a(c)):(e.consume(c),s)}function f(c){return c===96?(e.consume(c),i++,f):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),n(c)):(l.type="codeTextData",s(c))}}class X1{constructor(n){this.left=n?[...n]:[],this.right=[]}get(n){if(n<0||n>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+n+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return n<this.left.length?this.left[n]:this.right[this.right.length-n+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(n,t){const r=t??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(n,r):n>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-n+this.left.length).reverse():this.left.slice(n).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(n,t,r){const i=t||0;this.setCursor(Math.trunc(n));const l=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return r&&kr(this.left,r),l.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(n){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(n)}pushMany(n){this.setCursor(Number.POSITIVE_INFINITY),kr(this.left,n)}unshift(n){this.setCursor(0),this.right.push(n)}unshiftMany(n){this.setCursor(0),kr(this.right,n.reverse())}setCursor(n){if(!(n===this.left.length||n>this.left.length&&this.right.length===0||n<0&&this.left.length===0))if(n<this.left.length){const t=this.left.splice(n,Number.POSITIVE_INFINITY);kr(this.right,t.reverse())}else{const t=this.right.splice(this.left.length+this.right.length-n,Number.POSITIVE_INFINITY);kr(this.left,t.reverse())}}}function kr(e,n){let t=0;if(n.length<1e4)e.push(...n);else for(;t<n.length;)e.push(...n.slice(t,t+1e4)),t+=1e4}function Bd(e){const n={};let t=-1,r,i,l,o,u,a,s;const f=new X1(e);for(;++t<f.length;){for(;t in n;)t=n[t];if(r=f.get(t),t&&r[1].type==="chunkFlow"&&f.get(t-1)[1].type==="listItemPrefix"&&(a=r[1]._tokenizer.events,l=0,l<a.length&&a[l][1].type==="lineEndingBlank"&&(l+=2),l<a.length&&a[l][1].type==="content"))for(;++l<a.length&&a[l][1].type!=="content";)a[l][1].type==="chunkText"&&(a[l][1]._isInFirstContentOfListItem=!0,l++);if(r[0]==="enter")r[1].contentType&&(Object.assign(n,G1(f,t)),t=n[t],s=!0);else if(r[1]._container){for(l=t,i=void 0;l--;)if(o=f.get(l),o[1].type==="lineEnding"||o[1].type==="lineEndingBlank")o[0]==="enter"&&(i&&(f.get(i)[1].type="lineEndingBlank"),o[1].type="lineEnding",i=l);else if(!(o[1].type==="linePrefix"||o[1].type==="listItemIndent"))break;i&&(r[1].end={...f.get(i)[1].start},u=f.slice(i,t),u.unshift(r),f.splice(i,t-i+1,u))}}return Ye(e,0,Number.POSITIVE_INFINITY,f.slice(0)),!s}function G1(e,n){const t=e.get(n)[1],r=e.get(n)[2];let i=n-1;const l=[];let o=t._tokenizer;o||(o=r.parser[t.contentType](t.start),t._contentTypeTextTrailing&&(o._contentTypeTextTrailing=!0));const u=o.events,a=[],s={};let f,c,p=-1,d=t,g=0,v=0;const E=[v];for(;d;){for(;e.get(++i)[1]!==d;);l.push(i),d._tokenizer||(f=r.sliceStream(d),d.next||f.push(null),c&&o.defineSkip(d.start),d._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=!0),o.write(f),d._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=void 0)),c=d,d=d.next}for(d=t;++p<u.length;)u[p][0]==="exit"&&u[p-1][0]==="enter"&&u[p][1].type===u[p-1][1].type&&u[p][1].start.line!==u[p][1].end.line&&(v=p+1,E.push(v),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(o.events=[],d?(d._tokenizer=void 0,d.previous=void 0):E.pop(),p=E.length;p--;){const h=u.slice(E[p],E[p+1]),m=l.pop();a.push([m,m+h.length-1]),e.splice(m,2,h)}for(a.reverse(),p=-1;++p<a.length;)s[g+a[p][0]]=g+a[p][1],g+=a[p][1]-a[p][0]-1;return s}const Z1={resolve:ex,tokenize:nx},J1={partial:!0,tokenize:tx};function ex(e){return Bd(e),e}function nx(e,n){let t;return r;function r(u){return e.enter("content"),t=e.enter("chunkContent",{contentType:"content"}),i(u)}function i(u){return u===null?l(u):F(u)?e.check(J1,o,l)(u):(e.consume(u),i)}function l(u){return e.exit("chunkContent"),e.exit("content"),n(u)}function o(u){return e.consume(u),e.exit("chunkContent"),t.next=e.enter("chunkContent",{contentType:"content",previous:t}),t=t.next,i}}function tx(e,n,t){const r=this;return i;function i(o){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),Q(e,l,"linePrefix")}function l(o){if(o===null||F(o))return t(o);const u=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?n(o):e.interrupt(r.parser.constructs.flow,t,n)(o)}}function Ud(e,n,t,r,i,l,o,u,a){const s=a||Number.POSITIVE_INFINITY;let f=0;return c;function c(h){return h===60?(e.enter(r),e.enter(i),e.enter(l),e.consume(h),e.exit(l),p):h===null||h===32||h===41||xl(h)?t(h):(e.enter(r),e.enter(o),e.enter(u),e.enter("chunkString",{contentType:"string"}),v(h))}function p(h){return h===62?(e.enter(l),e.consume(h),e.exit(l),e.exit(i),e.exit(r),n):(e.enter(u),e.enter("chunkString",{contentType:"string"}),d(h))}function d(h){return h===62?(e.exit("chunkString"),e.exit(u),p(h)):h===null||h===60||F(h)?t(h):(e.consume(h),h===92?g:d)}function g(h){return h===60||h===62||h===92?(e.consume(h),d):d(h)}function v(h){return!f&&(h===null||h===41||J(h))?(e.exit("chunkString"),e.exit(u),e.exit(o),e.exit(r),n(h)):f<s&&h===40?(e.consume(h),f++,v):h===41?(e.consume(h),f--,v):h===null||h===32||h===40||xl(h)?t(h):(e.consume(h),h===92?E:v)}function E(h){return h===40||h===41||h===92?(e.consume(h),v):v(h)}}function $d(e,n,t,r,i,l){const o=this;let u=0,a;return s;function s(d){return e.enter(r),e.enter(i),e.consume(d),e.exit(i),e.enter(l),f}function f(d){return u>999||d===null||d===91||d===93&&!a||d===94&&!u&&"_hiddenFootnoteSupport"in o.parser.constructs?t(d):d===93?(e.exit(l),e.enter(i),e.consume(d),e.exit(i),e.exit(r),n):F(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),f):(e.enter("chunkString",{contentType:"string"}),c(d))}function c(d){return d===null||d===91||d===93||F(d)||u++>999?(e.exit("chunkString"),f(d)):(e.consume(d),a||(a=!$(d)),d===92?p:c)}function p(d){return d===91||d===92||d===93?(e.consume(d),u++,c):c(d)}}function Hd(e,n,t,r,i,l){let o;return u;function u(p){return p===34||p===39||p===40?(e.enter(r),e.enter(i),e.consume(p),e.exit(i),o=p===40?41:p,a):t(p)}function a(p){return p===o?(e.enter(i),e.consume(p),e.exit(i),e.exit(r),n):(e.enter(l),s(p))}function s(p){return p===o?(e.exit(l),a(o)):p===null?t(p):F(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),Q(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),f(p))}function f(p){return p===o||p===null||F(p)?(e.exit("chunkString"),s(p)):(e.consume(p),p===92?c:f)}function c(p){return p===o||p===92?(e.consume(p),f):f(p)}}function jr(e,n){let t;return r;function r(i){return F(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),t=!0,r):$(i)?Q(e,r,t?"linePrefix":"lineSuffix")(i):n(i)}}const rx={name:"definition",tokenize:lx},ix={partial:!0,tokenize:ox};function lx(e,n,t){const r=this;let i;return l;function l(d){return e.enter("definition"),o(d)}function o(d){return $d.call(r,e,u,t,"definitionLabel","definitionLabelMarker","definitionLabelString")(d)}function u(d){return i=hn(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),d===58?(e.enter("definitionMarker"),e.consume(d),e.exit("definitionMarker"),a):t(d)}function a(d){return J(d)?jr(e,s)(d):s(d)}function s(d){return Ud(e,f,t,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(d)}function f(d){return e.attempt(ix,c,c)(d)}function c(d){return $(d)?Q(e,p,"whitespace")(d):p(d)}function p(d){return d===null||F(d)?(e.exit("definition"),r.parser.defined.push(i),n(d)):t(d)}}function ox(e,n,t){return r;function r(u){return J(u)?jr(e,i)(u):t(u)}function i(u){return Hd(e,l,t,"definitionTitle","definitionTitleMarker","definitionTitleString")(u)}function l(u){return $(u)?Q(e,o,"whitespace")(u):o(u)}function o(u){return u===null||F(u)?n(u):t(u)}}const ux={name:"hardBreakEscape",tokenize:ax};function ax(e,n,t){return r;function r(l){return e.enter("hardBreakEscape"),e.consume(l),i}function i(l){return F(l)?(e.exit("hardBreakEscape"),n(l)):t(l)}}const sx={name:"headingAtx",resolve:cx,tokenize:fx};function cx(e,n){let t=e.length-2,r=3,i,l;return e[r][1].type==="whitespace"&&(r+=2),t-2>r&&e[t][1].type==="whitespace"&&(t-=2),e[t][1].type==="atxHeadingSequence"&&(r===t-1||t-4>r&&e[t-2][1].type==="whitespace")&&(t-=r+1===t?2:4),t>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[t][1].end},l={type:"chunkText",start:e[r][1].start,end:e[t][1].end,contentType:"text"},Ye(e,r,t-r+1,[["enter",i,n],["enter",l,n],["exit",l,n],["exit",i,n]])),e}function fx(e,n,t){let r=0;return i;function i(f){return e.enter("atxHeading"),l(f)}function l(f){return e.enter("atxHeadingSequence"),o(f)}function o(f){return f===35&&r++<6?(e.consume(f),o):f===null||J(f)?(e.exit("atxHeadingSequence"),u(f)):t(f)}function u(f){return f===35?(e.enter("atxHeadingSequence"),a(f)):f===null||F(f)?(e.exit("atxHeading"),n(f)):$(f)?Q(e,u,"whitespace")(f):(e.enter("atxHeadingText"),s(f))}function a(f){return f===35?(e.consume(f),a):(e.exit("atxHeadingSequence"),u(f))}function s(f){return f===null||f===35||J(f)?(e.exit("atxHeadingText"),u(f)):(e.consume(f),s)}}const px=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],bc=["pre","script","style","textarea"],dx={concrete:!0,name:"htmlFlow",resolveTo:gx,tokenize:yx},hx={partial:!0,tokenize:kx},mx={partial:!0,tokenize:xx};function gx(e){let n=e.length;for(;n--&&!(e[n][0]==="enter"&&e[n][1].type==="htmlFlow"););return n>1&&e[n-2][1].type==="linePrefix"&&(e[n][1].start=e[n-2][1].start,e[n+1][1].start=e[n-2][1].start,e.splice(n-2,2)),e}function yx(e,n,t){const r=this;let i,l,o,u,a;return s;function s(k){return f(k)}function f(k){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(k),c}function c(k){return k===33?(e.consume(k),p):k===47?(e.consume(k),l=!0,v):k===63?(e.consume(k),i=3,r.interrupt?n:x):Re(k)?(e.consume(k),o=String.fromCharCode(k),E):t(k)}function p(k){return k===45?(e.consume(k),i=2,d):k===91?(e.consume(k),i=5,u=0,g):Re(k)?(e.consume(k),i=4,r.interrupt?n:x):t(k)}function d(k){return k===45?(e.consume(k),r.interrupt?n:x):t(k)}function g(k){const ye="CDATA[";return k===ye.charCodeAt(u++)?(e.consume(k),u===ye.length?r.interrupt?n:N:g):t(k)}function v(k){return Re(k)?(e.consume(k),o=String.fromCharCode(k),E):t(k)}function E(k){if(k===null||k===47||k===62||J(k)){const ye=k===47,on=o.toLowerCase();return!ye&&!l&&bc.includes(on)?(i=1,r.interrupt?n(k):N(k)):px.includes(o.toLowerCase())?(i=6,ye?(e.consume(k),h):r.interrupt?n(k):N(k)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?t(k):l?m(k):y(k))}return k===45||Ie(k)?(e.consume(k),o+=String.fromCharCode(k),E):t(k)}function h(k){return k===62?(e.consume(k),r.interrupt?n:N):t(k)}function m(k){return $(k)?(e.consume(k),m):C(k)}function y(k){return k===47?(e.consume(k),C):k===58||k===95||Re(k)?(e.consume(k),T):$(k)?(e.consume(k),y):C(k)}function T(k){return k===45||k===46||k===58||k===95||Ie(k)?(e.consume(k),T):P(k)}function P(k){return k===61?(e.consume(k),S):$(k)?(e.consume(k),P):y(k)}function S(k){return k===null||k===60||k===61||k===62||k===96?t(k):k===34||k===39?(e.consume(k),a=k,_):$(k)?(e.consume(k),S):L(k)}function _(k){return k===a?(e.consume(k),a=null,M):k===null||F(k)?t(k):(e.consume(k),_)}function L(k){return k===null||k===34||k===39||k===47||k===60||k===61||k===62||k===96||J(k)?P(k):(e.consume(k),L)}function M(k){return k===47||k===62||$(k)?y(k):t(k)}function C(k){return k===62?(e.consume(k),D):t(k)}function D(k){return k===null||F(k)?N(k):$(k)?(e.consume(k),D):t(k)}function N(k){return k===45&&i===2?(e.consume(k),he):k===60&&i===1?(e.consume(k),pe):k===62&&i===4?(e.consume(k),Y):k===63&&i===3?(e.consume(k),x):k===93&&i===5?(e.consume(k),O):F(k)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(hx,G,q)(k)):k===null||F(k)?(e.exit("htmlFlowData"),q(k)):(e.consume(k),N)}function q(k){return e.check(mx,ee,G)(k)}function ee(k){return e.enter("lineEnding"),e.consume(k),e.exit("lineEnding"),V}function V(k){return k===null||F(k)?q(k):(e.enter("htmlFlowData"),N(k))}function he(k){return k===45?(e.consume(k),x):N(k)}function pe(k){return k===47?(e.consume(k),o="",A):N(k)}function A(k){if(k===62){const ye=o.toLowerCase();return bc.includes(ye)?(e.consume(k),Y):N(k)}return Re(k)&&o.length<8?(e.consume(k),o+=String.fromCharCode(k),A):N(k)}function O(k){return k===93?(e.consume(k),x):N(k)}function x(k){return k===62?(e.consume(k),Y):k===45&&i===2?(e.consume(k),x):N(k)}function Y(k){return k===null||F(k)?(e.exit("htmlFlowData"),G(k)):(e.consume(k),Y)}function G(k){return e.exit("htmlFlow"),n(k)}}function xx(e,n,t){const r=this;return i;function i(o){return F(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),l):t(o)}function l(o){return r.parser.lazy[r.now().line]?t(o):n(o)}}function kx(e,n,t){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(ai,n,t)}}const vx={name:"htmlText",tokenize:wx};function wx(e,n,t){const r=this;let i,l,o;return u;function u(x){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(x),a}function a(x){return x===33?(e.consume(x),s):x===47?(e.consume(x),P):x===63?(e.consume(x),y):Re(x)?(e.consume(x),L):t(x)}function s(x){return x===45?(e.consume(x),f):x===91?(e.consume(x),l=0,g):Re(x)?(e.consume(x),m):t(x)}function f(x){return x===45?(e.consume(x),d):t(x)}function c(x){return x===null?t(x):x===45?(e.consume(x),p):F(x)?(o=c,pe(x)):(e.consume(x),c)}function p(x){return x===45?(e.consume(x),d):c(x)}function d(x){return x===62?he(x):x===45?p(x):c(x)}function g(x){const Y="CDATA[";return x===Y.charCodeAt(l++)?(e.consume(x),l===Y.length?v:g):t(x)}function v(x){return x===null?t(x):x===93?(e.consume(x),E):F(x)?(o=v,pe(x)):(e.consume(x),v)}function E(x){return x===93?(e.consume(x),h):v(x)}function h(x){return x===62?he(x):x===93?(e.consume(x),h):v(x)}function m(x){return x===null||x===62?he(x):F(x)?(o=m,pe(x)):(e.consume(x),m)}function y(x){return x===null?t(x):x===63?(e.consume(x),T):F(x)?(o=y,pe(x)):(e.consume(x),y)}function T(x){return x===62?he(x):y(x)}function P(x){return Re(x)?(e.consume(x),S):t(x)}function S(x){return x===45||Ie(x)?(e.consume(x),S):_(x)}function _(x){return F(x)?(o=_,pe(x)):$(x)?(e.consume(x),_):he(x)}function L(x){return x===45||Ie(x)?(e.consume(x),L):x===47||x===62||J(x)?M(x):t(x)}function M(x){return x===47?(e.consume(x),he):x===58||x===95||Re(x)?(e.consume(x),C):F(x)?(o=M,pe(x)):$(x)?(e.consume(x),M):he(x)}function C(x){return x===45||x===46||x===58||x===95||Ie(x)?(e.consume(x),C):D(x)}function D(x){return x===61?(e.consume(x),N):F(x)?(o=D,pe(x)):$(x)?(e.consume(x),D):M(x)}function N(x){return x===null||x===60||x===61||x===62||x===96?t(x):x===34||x===39?(e.consume(x),i=x,q):F(x)?(o=N,pe(x)):$(x)?(e.consume(x),N):(e.consume(x),ee)}function q(x){return x===i?(e.consume(x),i=void 0,V):x===null?t(x):F(x)?(o=q,pe(x)):(e.consume(x),q)}function ee(x){return x===null||x===34||x===39||x===60||x===61||x===96?t(x):x===47||x===62||J(x)?M(x):(e.consume(x),ee)}function V(x){return x===47||x===62||J(x)?M(x):t(x)}function he(x){return x===62?(e.consume(x),e.exit("htmlTextData"),e.exit("htmlText"),n):t(x)}function pe(x){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(x),e.exit("lineEnding"),A}function A(x){return $(x)?Q(e,O,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(x):O(x)}function O(x){return e.enter("htmlTextData"),o(x)}}const $a={name:"labelEnd",resolveAll:Tx,resolveTo:zx,tokenize:Px},Sx={tokenize:Ix},Cx={tokenize:_x},Ex={tokenize:bx};function Tx(e){let n=-1;const t=[];for(;++n<e.length;){const r=e[n][1];if(t.push(e[n]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){const i=r.type==="labelImage"?4:2;r.type="data",n+=i}}return e.length!==t.length&&Ye(e,0,e.length,t),e}function zx(e,n){let t=e.length,r=0,i,l,o,u;for(;t--;)if(i=e[t][1],l){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[t][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(o){if(e[t][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(l=t,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(o=t);const a={type:e[l][1].type==="labelLink"?"link":"image",start:{...e[l][1].start},end:{...e[e.length-1][1].end}},s={type:"label",start:{...e[l][1].start},end:{...e[o][1].end}},f={type:"labelText",start:{...e[l+r+2][1].end},end:{...e[o-2][1].start}};return u=[["enter",a,n],["enter",s,n]],u=en(u,e.slice(l+1,l+r+3)),u=en(u,[["enter",f,n]]),u=en(u,Bl(n.parser.constructs.insideSpan.null,e.slice(l+r+4,o-3),n)),u=en(u,[["exit",f,n],e[o-2],e[o-1],["exit",s,n]]),u=en(u,e.slice(o+1)),u=en(u,[["exit",a,n]]),Ye(e,l,e.length,u),e}function Px(e,n,t){const r=this;let i=r.events.length,l,o;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){l=r.events[i][1];break}return u;function u(p){return l?l._inactive?c(p):(o=r.parser.defined.includes(hn(r.sliceSerialize({start:l.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(p),e.exit("labelMarker"),e.exit("labelEnd"),a):t(p)}function a(p){return p===40?e.attempt(Sx,f,o?f:c)(p):p===91?e.attempt(Cx,f,o?s:c)(p):o?f(p):c(p)}function s(p){return e.attempt(Ex,f,c)(p)}function f(p){return n(p)}function c(p){return l._balanced=!0,t(p)}}function Ix(e,n,t){return r;function r(c){return e.enter("resource"),e.enter("resourceMarker"),e.consume(c),e.exit("resourceMarker"),i}function i(c){return J(c)?jr(e,l)(c):l(c)}function l(c){return c===41?f(c):Ud(e,o,u,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(c)}function o(c){return J(c)?jr(e,a)(c):f(c)}function u(c){return t(c)}function a(c){return c===34||c===39||c===40?Hd(e,s,t,"resourceTitle","resourceTitleMarker","resourceTitleString")(c):f(c)}function s(c){return J(c)?jr(e,f)(c):f(c)}function f(c){return c===41?(e.enter("resourceMarker"),e.consume(c),e.exit("resourceMarker"),e.exit("resource"),n):t(c)}}function _x(e,n,t){const r=this;return i;function i(u){return $d.call(r,e,l,o,"reference","referenceMarker","referenceString")(u)}function l(u){return r.parser.defined.includes(hn(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?n(u):t(u)}function o(u){return t(u)}}function bx(e,n,t){return r;function r(l){return e.enter("reference"),e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),i}function i(l){return l===93?(e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),e.exit("reference"),n):t(l)}}const Lx={name:"labelStartImage",resolveAll:$a.resolveAll,tokenize:Rx};function Rx(e,n,t){const r=this;return i;function i(u){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(u),e.exit("labelImageMarker"),l}function l(u){return u===91?(e.enter("labelMarker"),e.consume(u),e.exit("labelMarker"),e.exit("labelImage"),o):t(u)}function o(u){return u===94&&"_hiddenFootnoteSupport"in r.parser.constructs?t(u):n(u)}}const Ax={name:"labelStartLink",resolveAll:$a.resolveAll,tokenize:Dx};function Dx(e,n,t){const r=this;return i;function i(o){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(o),e.exit("labelMarker"),e.exit("labelLink"),l}function l(o){return o===94&&"_hiddenFootnoteSupport"in r.parser.constructs?t(o):n(o)}}const vo={name:"lineEnding",tokenize:jx};function jx(e,n){return t;function t(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),Q(e,n,"linePrefix")}}const Wi={name:"thematicBreak",tokenize:Nx};function Nx(e,n,t){let r=0,i;return l;function l(s){return e.enter("thematicBreak"),o(s)}function o(s){return i=s,u(s)}function u(s){return s===i?(e.enter("thematicBreakSequence"),a(s)):r>=3&&(s===null||F(s))?(e.exit("thematicBreak"),n(s)):t(s)}function a(s){return s===i?(e.consume(s),r++,a):(e.exit("thematicBreakSequence"),$(s)?Q(e,u,"whitespace")(s):u(s))}}const Ne={continuation:{tokenize:Bx},exit:$x,name:"list",tokenize:Ox},Fx={partial:!0,tokenize:Hx},Mx={partial:!0,tokenize:Ux};function Ox(e,n,t){const r=this,i=r.events[r.events.length-1];let l=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,o=0;return u;function u(d){const g=r.containerState.type||(d===42||d===43||d===45?"listUnordered":"listOrdered");if(g==="listUnordered"?!r.containerState.marker||d===r.containerState.marker:Ru(d)){if(r.containerState.type||(r.containerState.type=g,e.enter(g,{_container:!0})),g==="listUnordered")return e.enter("listItemPrefix"),d===42||d===45?e.check(Wi,t,s)(d):s(d);if(!r.interrupt||d===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),a(d)}return t(d)}function a(d){return Ru(d)&&++o<10?(e.consume(d),a):(!r.interrupt||o<2)&&(r.containerState.marker?d===r.containerState.marker:d===41||d===46)?(e.exit("listItemValue"),s(d)):t(d)}function s(d){return e.enter("listItemMarker"),e.consume(d),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||d,e.check(ai,r.interrupt?t:f,e.attempt(Fx,p,c))}function f(d){return r.containerState.initialBlankLine=!0,l++,p(d)}function c(d){return $(d)?(e.enter("listItemPrefixWhitespace"),e.consume(d),e.exit("listItemPrefixWhitespace"),p):t(d)}function p(d){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,n(d)}}function Bx(e,n,t){const r=this;return r.containerState._closeFlow=void 0,e.check(ai,i,l);function i(u){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,Q(e,n,"listItemIndent",r.containerState.size+1)(u)}function l(u){return r.containerState.furtherBlankLines||!$(u)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(u)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(Mx,n,o)(u))}function o(u){return r.containerState._closeFlow=!0,r.interrupt=void 0,Q(e,e.attempt(Ne,n,t),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(u)}}function Ux(e,n,t){const r=this;return Q(e,i,"listItemIndent",r.containerState.size+1);function i(l){const o=r.events[r.events.length-1];return o&&o[1].type==="listItemIndent"&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?n(l):t(l)}}function $x(e){e.exit(this.containerState.type)}function Hx(e,n,t){const r=this;return Q(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4+1);function i(l){const o=r.events[r.events.length-1];return!$(l)&&o&&o[1].type==="listItemPrefixWhitespace"?n(l):t(l)}}const Lc={name:"setextUnderline",resolveTo:Vx,tokenize:Wx};function Vx(e,n){let t=e.length,r,i,l;for(;t--;)if(e[t][0]==="enter"){if(e[t][1].type==="content"){r=t;break}e[t][1].type==="paragraph"&&(i=t)}else e[t][1].type==="content"&&e.splice(t,1),!l&&e[t][1].type==="definition"&&(l=t);const o={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[i][1].type="setextHeadingText",l?(e.splice(i,0,["enter",o,n]),e.splice(l+1,0,["exit",e[r][1],n]),e[r][1].end={...e[l][1].end}):e[r][1]=o,e.push(["exit",o,n]),e}function Wx(e,n,t){const r=this;let i;return l;function l(s){let f=r.events.length,c;for(;f--;)if(r.events[f][1].type!=="lineEnding"&&r.events[f][1].type!=="linePrefix"&&r.events[f][1].type!=="content"){c=r.events[f][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||c)?(e.enter("setextHeadingLine"),i=s,o(s)):t(s)}function o(s){return e.enter("setextHeadingLineSequence"),u(s)}function u(s){return s===i?(e.consume(s),u):(e.exit("setextHeadingLineSequence"),$(s)?Q(e,a,"lineSuffix")(s):a(s))}function a(s){return s===null||F(s)?(e.exit("setextHeadingLine"),n(s)):t(s)}}const Qx={tokenize:qx};function qx(e){const n=this,t=e.attempt(ai,r,e.attempt(this.parser.constructs.flowInitial,i,Q(e,e.attempt(this.parser.constructs.flow,i,e.attempt(Z1,i)),"linePrefix")));return t;function r(l){if(l===null){e.consume(l);return}return e.enter("lineEndingBlank"),e.consume(l),e.exit("lineEndingBlank"),n.currentConstruct=void 0,t}function i(l){if(l===null){e.consume(l);return}return e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),n.currentConstruct=void 0,t}}const Yx={resolveAll:Wd()},Kx=Vd("string"),Xx=Vd("text");function Vd(e){return{resolveAll:Wd(e==="text"?Gx:void 0),tokenize:n};function n(t){const r=this,i=this.parser.constructs[e],l=t.attempt(i,o,u);return o;function o(f){return s(f)?l(f):u(f)}function u(f){if(f===null){t.consume(f);return}return t.enter("data"),t.consume(f),a}function a(f){return s(f)?(t.exit("data"),l(f)):(t.consume(f),a)}function s(f){if(f===null)return!0;const c=i[f];let p=-1;if(c)for(;++p<c.length;){const d=c[p];if(!d.previous||d.previous.call(r,r.previous))return!0}return!1}}}function Wd(e){return n;function n(t,r){let i=-1,l;for(;++i<=t.length;)l===void 0?t[i]&&t[i][1].type==="data"&&(l=i,i++):(!t[i]||t[i][1].type!=="data")&&(i!==l+2&&(t[l][1].end=t[i-1][1].end,t.splice(l+2,i-l-2),i=l+2),l=void 0);return e?e(t,r):t}}function Gx(e,n){let t=0;for(;++t<=e.length;)if((t===e.length||e[t][1].type==="lineEnding")&&e[t-1][1].type==="data"){const r=e[t-1][1],i=n.sliceStream(r);let l=i.length,o=-1,u=0,a;for(;l--;){const s=i[l];if(typeof s=="string"){for(o=s.length;s.charCodeAt(o-1)===32;)u++,o--;if(o)break;o=-1}else if(s===-2)a=!0,u++;else if(s!==-1){l++;break}}if(n._contentTypeTextTrailing&&t===e.length&&(u=0),u){const s={type:t===e.length||a||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?o:r.start._bufferIndex+o,_index:r.start._index+l,line:r.end.line,column:r.end.column-u,offset:r.end.offset-u},end:{...r.end}};r.end={...s.start},r.start.offset===r.end.offset?Object.assign(r,s):(e.splice(t,0,["enter",s,n],["exit",s,n]),t+=2)}t++}return e}const Zx={[42]:Ne,[43]:Ne,[45]:Ne,[48]:Ne,[49]:Ne,[50]:Ne,[51]:Ne,[52]:Ne,[53]:Ne,[54]:Ne,[55]:Ne,[56]:Ne,[57]:Ne,[62]:Fd},Jx={[91]:rx},e0={[-2]:ko,[-1]:ko,[32]:ko},n0={[35]:sx,[42]:Wi,[45]:[Lc,Wi],[60]:dx,[61]:Lc,[95]:Wi,[96]:_c,[126]:_c},t0={[38]:Od,[92]:Md},r0={[-5]:vo,[-4]:vo,[-3]:vo,[33]:Lx,[38]:Od,[42]:Au,[60]:[A1,vx],[91]:Ax,[92]:[ux,Md],[93]:$a,[95]:Au,[96]:Q1},i0={null:[Au,Yx]},l0={null:[42,95]},o0={null:[]},u0=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:l0,contentInitial:Jx,disable:o0,document:Zx,flow:n0,flowInitial:e0,insideSpan:i0,string:t0,text:r0},Symbol.toStringTag,{value:"Module"}));function a0(e,n,t){let r={_bufferIndex:-1,_index:0,line:t&&t.line||1,column:t&&t.column||1,offset:t&&t.offset||0};const i={},l=[];let o=[],u=[];const a={attempt:_(P),check:_(S),consume:m,enter:y,exit:T,interrupt:_(S,{interrupt:!0})},s={code:null,containerState:{},defineSkip:v,events:[],now:g,parser:e,previous:null,sliceSerialize:p,sliceStream:d,write:c};let f=n.tokenize.call(s,a);return n.resolveAll&&l.push(n),s;function c(D){return o=en(o,D),E(),o[o.length-1]!==null?[]:(L(n,0),s.events=Bl(l,s.events,s),s.events)}function p(D,N){return c0(d(D),N)}function d(D){return s0(o,D)}function g(){const{_bufferIndex:D,_index:N,line:q,column:ee,offset:V}=r;return{_bufferIndex:D,_index:N,line:q,column:ee,offset:V}}function v(D){i[D.line]=D.column,C()}function E(){let D;for(;r._index<o.length;){const N=o[r._index];if(typeof N=="string")for(D=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===D&&r._bufferIndex<N.length;)h(N.charCodeAt(r._bufferIndex));else h(N)}}function h(D){f=f(D)}function m(D){F(D)?(r.line++,r.column=1,r.offset+=D===-3?2:1,C()):D!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),s.previous=D}function y(D,N){const q=N||{};return q.type=D,q.start=g(),s.events.push(["enter",q,s]),u.push(q),q}function T(D){const N=u.pop();return N.end=g(),s.events.push(["exit",N,s]),N}function P(D,N){L(D,N.from)}function S(D,N){N.restore()}function _(D,N){return q;function q(ee,V,he){let pe,A,O,x;return Array.isArray(ee)?G(ee):"tokenize"in ee?G([ee]):Y(ee);function Y(re){return gn;function gn(Fn){const Tt=Fn!==null&&re[Fn],zt=Fn!==null&&re.null,fi=[...Array.isArray(Tt)?Tt:Tt?[Tt]:[],...Array.isArray(zt)?zt:zt?[zt]:[]];return G(fi)(Fn)}}function G(re){return pe=re,A=0,re.length===0?he:k(re[A])}function k(re){return gn;function gn(Fn){return x=M(),O=re,re.partial||(s.currentConstruct=re),re.name&&s.parser.constructs.disable.null.includes(re.name)?on():re.tokenize.call(N?Object.assign(Object.create(s),N):s,a,ye,on)(Fn)}}function ye(re){return D(O,x),V}function on(re){return x.restore(),++A<pe.length?k(pe[A]):he}}}function L(D,N){D.resolveAll&&!l.includes(D)&&l.push(D),D.resolve&&Ye(s.events,N,s.events.length-N,D.resolve(s.events.slice(N),s)),D.resolveTo&&(s.events=D.resolveTo(s.events,s))}function M(){const D=g(),N=s.previous,q=s.currentConstruct,ee=s.events.length,V=Array.from(u);return{from:ee,restore:he};function he(){r=D,s.previous=N,s.currentConstruct=q,s.events.length=ee,u=V,C()}}function C(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function s0(e,n){const t=n.start._index,r=n.start._bufferIndex,i=n.end._index,l=n.end._bufferIndex;let o;if(t===i)o=[e[t].slice(r,l)];else{if(o=e.slice(t,i),r>-1){const u=o[0];typeof u=="string"?o[0]=u.slice(r):o.shift()}l>0&&o.push(e[i].slice(0,l))}return o}function c0(e,n){let t=-1;const r=[];let i;for(;++t<e.length;){const l=e[t];let o;if(typeof l=="string")o=l;else switch(l){case-5:{o="\r";break}case-4:{o=`
`;break}case-3:{o=`\r
`;break}case-2:{o=n?" ":"	";break}case-1:{if(!n&&i)continue;o=" ";break}default:o=String.fromCharCode(l)}i=l===-2,r.push(o)}return r.join("")}function f0(e){const r={constructs:jd([u0,...(e||{}).extensions||[]]),content:i(z1),defined:[],document:i(I1),flow:i(Qx),lazy:{},string:i(Kx),text:i(Xx)};return r;function i(l){return o;function o(u){return a0(r,l,u)}}}function p0(e){for(;!Bd(e););return e}const Rc=/[\0\t\n\r]/g;function d0(){let e=1,n="",t=!0,r;return i;function i(l,o,u){const a=[];let s,f,c,p,d;for(l=n+(typeof l=="string"?l.toString():new TextDecoder(o||void 0).decode(l)),c=0,n="",t&&(l.charCodeAt(0)===65279&&c++,t=void 0);c<l.length;){if(Rc.lastIndex=c,s=Rc.exec(l),p=s&&s.index!==void 0?s.index:l.length,d=l.charCodeAt(p),!s){n=l.slice(c);break}if(d===10&&c===p&&r)a.push(-3),r=void 0;else switch(r&&(a.push(-5),r=void 0),c<p&&(a.push(l.slice(c,p)),e+=p-c),d){case 0:{a.push(65533),e++;break}case 9:{for(f=Math.ceil(e/4)*4,a.push(-2);e++<f;)a.push(-1);break}case 10:{a.push(-4),e=1;break}default:r=!0,e=1}c=p+1}return u&&(r&&a.push(-5),n&&a.push(n),a.push(null)),a}}const h0=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function m0(e){return e.replace(h0,g0)}function g0(e,n,t){if(n)return n;if(t.charCodeAt(0)===35){const i=t.charCodeAt(1),l=i===120||i===88;return Nd(t.slice(l?2:1),l?16:10)}return Ua(t)||e}const Qd={}.hasOwnProperty;function y0(e,n,t){return typeof n!="string"&&(t=n,n=void 0),x0(t)(p0(f0(t).document().write(d0()(e,n,!0))))}function x0(e){const n={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:l(ns),autolinkProtocol:M,autolinkEmail:M,atxHeading:l(Za),blockQuote:l(zt),characterEscape:M,characterReference:M,codeFenced:l(fi),codeFencedFenceInfo:o,codeFencedFenceMeta:o,codeIndented:l(fi,o),codeText:l(Ph,o),codeTextData:M,data:M,codeFlowValue:M,definition:l(Ih),definitionDestinationString:o,definitionLabelString:o,definitionTitleString:o,emphasis:l(_h),hardBreakEscape:l(Ja),hardBreakTrailing:l(Ja),htmlFlow:l(es,o),htmlFlowData:M,htmlText:l(es,o),htmlTextData:M,image:l(bh),label:o,link:l(ns),listItem:l(Lh),listItemValue:p,listOrdered:l(ts,c),listUnordered:l(ts),paragraph:l(Rh),reference:k,referenceString:o,resourceDestinationString:o,resourceTitleString:o,setextHeading:l(Za),strong:l(Ah),thematicBreak:l(jh)},exit:{atxHeading:a(),atxHeadingSequence:P,autolink:a(),autolinkEmail:Tt,autolinkProtocol:Fn,blockQuote:a(),characterEscapeValue:C,characterReferenceMarkerHexadecimal:on,characterReferenceMarkerNumeric:on,characterReferenceValue:re,characterReference:gn,codeFenced:a(E),codeFencedFence:v,codeFencedFenceInfo:d,codeFencedFenceMeta:g,codeFlowValue:C,codeIndented:a(h),codeText:a(V),codeTextData:C,data:C,definition:a(),definitionDestinationString:T,definitionLabelString:m,definitionTitleString:y,emphasis:a(),hardBreakEscape:a(N),hardBreakTrailing:a(N),htmlFlow:a(q),htmlFlowData:C,htmlText:a(ee),htmlTextData:C,image:a(pe),label:O,labelText:A,lineEnding:D,link:a(he),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:ye,resourceDestinationString:x,resourceTitleString:Y,resource:G,setextHeading:a(L),setextHeadingLineSequence:_,setextHeadingText:S,strong:a(),thematicBreak:a()}};qd(n,(e||{}).mdastExtensions||[]);const t={};return r;function r(z){let R={type:"root",children:[]};const B={stack:[R],tokenStack:[],config:n,enter:u,exit:s,buffer:o,resume:f,data:t},W=[];let Z=-1;for(;++Z<z.length;)if(z[Z][1].type==="listOrdered"||z[Z][1].type==="listUnordered")if(z[Z][0]==="enter")W.push(Z);else{const un=W.pop();Z=i(z,un,Z)}for(Z=-1;++Z<z.length;){const un=n[z[Z][0]];Qd.call(un,z[Z][1].type)&&un[z[Z][1].type].call(Object.assign({sliceSerialize:z[Z][2].sliceSerialize},B),z[Z][1])}if(B.tokenStack.length>0){const un=B.tokenStack[B.tokenStack.length-1];(un[1]||Ac).call(B,void 0,un[0])}for(R.position={start:On(z.length>0?z[0][1].start:{line:1,column:1,offset:0}),end:On(z.length>0?z[z.length-2][1].end:{line:1,column:1,offset:0})},Z=-1;++Z<n.transforms.length;)R=n.transforms[Z](R)||R;return R}function i(z,R,B){let W=R-1,Z=-1,un=!1,ot,En,sr,cr;for(;++W<=B;){const He=z[W];switch(He[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{He[0]==="enter"?Z++:Z--,cr=void 0;break}case"lineEndingBlank":{He[0]==="enter"&&(ot&&!cr&&!Z&&!sr&&(sr=W),cr=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:cr=void 0}if(!Z&&He[0]==="enter"&&He[1].type==="listItemPrefix"||Z===-1&&He[0]==="exit"&&(He[1].type==="listUnordered"||He[1].type==="listOrdered")){if(ot){let Pt=W;for(En=void 0;Pt--;){const Tn=z[Pt];if(Tn[1].type==="lineEnding"||Tn[1].type==="lineEndingBlank"){if(Tn[0]==="exit")continue;En&&(z[En][1].type="lineEndingBlank",un=!0),Tn[1].type="lineEnding",En=Pt}else if(!(Tn[1].type==="linePrefix"||Tn[1].type==="blockQuotePrefix"||Tn[1].type==="blockQuotePrefixWhitespace"||Tn[1].type==="blockQuoteMarker"||Tn[1].type==="listItemIndent"))break}sr&&(!En||sr<En)&&(ot._spread=!0),ot.end=Object.assign({},En?z[En][1].start:He[1].end),z.splice(En||W,0,["exit",ot,He[2]]),W++,B++}if(He[1].type==="listItemPrefix"){const Pt={type:"listItem",_spread:!1,start:Object.assign({},He[1].start),end:void 0};ot=Pt,z.splice(W,0,["enter",Pt,He[2]]),W++,B++,sr=void 0,cr=!0}}}return z[R][1]._spread=un,B}function l(z,R){return B;function B(W){u.call(this,z(W),W),R&&R.call(this,W)}}function o(){this.stack.push({type:"fragment",children:[]})}function u(z,R,B){this.stack[this.stack.length-1].children.push(z),this.stack.push(z),this.tokenStack.push([R,B||void 0]),z.position={start:On(R.start),end:void 0}}function a(z){return R;function R(B){z&&z.call(this,B),s.call(this,B)}}function s(z,R){const B=this.stack.pop(),W=this.tokenStack.pop();if(W)W[0].type!==z.type&&(R?R.call(this,z,W[0]):(W[1]||Ac).call(this,z,W[0]));else throw new Error("Cannot close `"+z.type+"` ("+Dr({start:z.start,end:z.end})+"): it’s not open");B.position.end=On(z.end)}function f(){return Ba(this.stack.pop())}function c(){this.data.expectingFirstListItemValue=!0}function p(z){if(this.data.expectingFirstListItemValue){const R=this.stack[this.stack.length-2];R.start=Number.parseInt(this.sliceSerialize(z),10),this.data.expectingFirstListItemValue=void 0}}function d(){const z=this.resume(),R=this.stack[this.stack.length-1];R.lang=z}function g(){const z=this.resume(),R=this.stack[this.stack.length-1];R.meta=z}function v(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function E(){const z=this.resume(),R=this.stack[this.stack.length-1];R.value=z.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function h(){const z=this.resume(),R=this.stack[this.stack.length-1];R.value=z.replace(/(\r?\n|\r)$/g,"")}function m(z){const R=this.resume(),B=this.stack[this.stack.length-1];B.label=R,B.identifier=hn(this.sliceSerialize(z)).toLowerCase()}function y(){const z=this.resume(),R=this.stack[this.stack.length-1];R.title=z}function T(){const z=this.resume(),R=this.stack[this.stack.length-1];R.url=z}function P(z){const R=this.stack[this.stack.length-1];if(!R.depth){const B=this.sliceSerialize(z).length;R.depth=B}}function S(){this.data.setextHeadingSlurpLineEnding=!0}function _(z){const R=this.stack[this.stack.length-1];R.depth=this.sliceSerialize(z).codePointAt(0)===61?1:2}function L(){this.data.setextHeadingSlurpLineEnding=void 0}function M(z){const B=this.stack[this.stack.length-1].children;let W=B[B.length-1];(!W||W.type!=="text")&&(W=Dh(),W.position={start:On(z.start),end:void 0},B.push(W)),this.stack.push(W)}function C(z){const R=this.stack.pop();R.value+=this.sliceSerialize(z),R.position.end=On(z.end)}function D(z){const R=this.stack[this.stack.length-1];if(this.data.atHardBreak){const B=R.children[R.children.length-1];B.position.end=On(z.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&n.canContainEols.includes(R.type)&&(M.call(this,z),C.call(this,z))}function N(){this.data.atHardBreak=!0}function q(){const z=this.resume(),R=this.stack[this.stack.length-1];R.value=z}function ee(){const z=this.resume(),R=this.stack[this.stack.length-1];R.value=z}function V(){const z=this.resume(),R=this.stack[this.stack.length-1];R.value=z}function he(){const z=this.stack[this.stack.length-1];if(this.data.inReference){const R=this.data.referenceType||"shortcut";z.type+="Reference",z.referenceType=R,delete z.url,delete z.title}else delete z.identifier,delete z.label;this.data.referenceType=void 0}function pe(){const z=this.stack[this.stack.length-1];if(this.data.inReference){const R=this.data.referenceType||"shortcut";z.type+="Reference",z.referenceType=R,delete z.url,delete z.title}else delete z.identifier,delete z.label;this.data.referenceType=void 0}function A(z){const R=this.sliceSerialize(z),B=this.stack[this.stack.length-2];B.label=m0(R),B.identifier=hn(R).toLowerCase()}function O(){const z=this.stack[this.stack.length-1],R=this.resume(),B=this.stack[this.stack.length-1];if(this.data.inReference=!0,B.type==="link"){const W=z.children;B.children=W}else B.alt=R}function x(){const z=this.resume(),R=this.stack[this.stack.length-1];R.url=z}function Y(){const z=this.resume(),R=this.stack[this.stack.length-1];R.title=z}function G(){this.data.inReference=void 0}function k(){this.data.referenceType="collapsed"}function ye(z){const R=this.resume(),B=this.stack[this.stack.length-1];B.label=R,B.identifier=hn(this.sliceSerialize(z)).toLowerCase(),this.data.referenceType="full"}function on(z){this.data.characterReferenceType=z.type}function re(z){const R=this.sliceSerialize(z),B=this.data.characterReferenceType;let W;B?(W=Nd(R,B==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):W=Ua(R);const Z=this.stack[this.stack.length-1];Z.value+=W}function gn(z){const R=this.stack.pop();R.position.end=On(z.end)}function Fn(z){C.call(this,z);const R=this.stack[this.stack.length-1];R.url=this.sliceSerialize(z)}function Tt(z){C.call(this,z);const R=this.stack[this.stack.length-1];R.url="mailto:"+this.sliceSerialize(z)}function zt(){return{type:"blockquote",children:[]}}function fi(){return{type:"code",lang:null,meta:null,value:""}}function Ph(){return{type:"inlineCode",value:""}}function Ih(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function _h(){return{type:"emphasis",children:[]}}function Za(){return{type:"heading",depth:0,children:[]}}function Ja(){return{type:"break"}}function es(){return{type:"html",value:""}}function bh(){return{type:"image",title:null,url:"",alt:null}}function ns(){return{type:"link",title:null,url:"",children:[]}}function ts(z){return{type:"list",ordered:z.type==="listOrdered",start:null,spread:z._spread,children:[]}}function Lh(z){return{type:"listItem",spread:z._spread,checked:null,children:[]}}function Rh(){return{type:"paragraph",children:[]}}function Ah(){return{type:"strong",children:[]}}function Dh(){return{type:"text",value:""}}function jh(){return{type:"thematicBreak"}}}function On(e){return{line:e.line,column:e.column,offset:e.offset}}function qd(e,n){let t=-1;for(;++t<n.length;){const r=n[t];Array.isArray(r)?qd(e,r):k0(e,r)}}function k0(e,n){let t;for(t in n)if(Qd.call(n,t))switch(t){case"canContainEols":{const r=n[t];r&&e[t].push(...r);break}case"transforms":{const r=n[t];r&&e[t].push(...r);break}case"enter":case"exit":{const r=n[t];r&&Object.assign(e[t],r);break}}}function Ac(e,n){throw e?new Error("Cannot close `"+e.type+"` ("+Dr({start:e.start,end:e.end})+"): a different token (`"+n.type+"`, "+Dr({start:n.start,end:n.end})+") is open"):new Error("Cannot close document, a token (`"+n.type+"`, "+Dr({start:n.start,end:n.end})+") is still open")}function v0(e){const n=this;n.parser=t;function t(r){return y0(r,{...n.data("settings"),...e,extensions:n.data("micromarkExtensions")||[],mdastExtensions:n.data("fromMarkdownExtensions")||[]})}}function w0(e,n){const t={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(n),!0)};return e.patch(n,t),e.applyData(n,t)}function S0(e,n){const t={type:"element",tagName:"br",properties:{},children:[]};return e.patch(n,t),[e.applyData(n,t),{type:"text",value:`
`}]}function C0(e,n){const t=n.value?n.value+`
`:"",r={};n.lang&&(r.className=["language-"+n.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:t}]};return n.meta&&(i.data={meta:n.meta}),e.patch(n,i),i=e.applyData(n,i),i={type:"element",tagName:"pre",properties:{},children:[i]},e.patch(n,i),i}function E0(e,n){const t={type:"element",tagName:"del",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function T0(e,n){const t={type:"element",tagName:"em",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function z0(e,n){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(n.identifier).toUpperCase(),i=ar(r.toLowerCase()),l=e.footnoteOrder.indexOf(r);let o,u=e.footnoteCounts.get(r);u===void 0?(u=0,e.footnoteOrder.push(r),o=e.footnoteOrder.length):o=l+1,u+=1,e.footnoteCounts.set(r,u);const a={type:"element",tagName:"a",properties:{href:"#"+t+"fn-"+i,id:t+"fnref-"+i+(u>1?"-"+u:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(o)}]};e.patch(n,a);const s={type:"element",tagName:"sup",properties:{},children:[a]};return e.patch(n,s),e.applyData(n,s)}function P0(e,n){const t={type:"element",tagName:"h"+n.depth,properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function I0(e,n){if(e.options.allowDangerousHtml){const t={type:"raw",value:n.value};return e.patch(n,t),e.applyData(n,t)}}function Yd(e,n){const t=n.referenceType;let r="]";if(t==="collapsed"?r+="[]":t==="full"&&(r+="["+(n.label||n.identifier)+"]"),n.type==="imageReference")return[{type:"text",value:"!["+n.alt+r}];const i=e.all(n),l=i[0];l&&l.type==="text"?l.value="["+l.value:i.unshift({type:"text",value:"["});const o=i[i.length-1];return o&&o.type==="text"?o.value+=r:i.push({type:"text",value:r}),i}function _0(e,n){const t=String(n.identifier).toUpperCase(),r=e.definitionById.get(t);if(!r)return Yd(e,n);const i={src:ar(r.url||""),alt:n.alt};r.title!==null&&r.title!==void 0&&(i.title=r.title);const l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(n,l),e.applyData(n,l)}function b0(e,n){const t={src:ar(n.url)};n.alt!==null&&n.alt!==void 0&&(t.alt=n.alt),n.title!==null&&n.title!==void 0&&(t.title=n.title);const r={type:"element",tagName:"img",properties:t,children:[]};return e.patch(n,r),e.applyData(n,r)}function L0(e,n){const t={type:"text",value:n.value.replace(/\r?\n|\r/g," ")};e.patch(n,t);const r={type:"element",tagName:"code",properties:{},children:[t]};return e.patch(n,r),e.applyData(n,r)}function R0(e,n){const t=String(n.identifier).toUpperCase(),r=e.definitionById.get(t);if(!r)return Yd(e,n);const i={href:ar(r.url||"")};r.title!==null&&r.title!==void 0&&(i.title=r.title);const l={type:"element",tagName:"a",properties:i,children:e.all(n)};return e.patch(n,l),e.applyData(n,l)}function A0(e,n){const t={href:ar(n.url)};n.title!==null&&n.title!==void 0&&(t.title=n.title);const r={type:"element",tagName:"a",properties:t,children:e.all(n)};return e.patch(n,r),e.applyData(n,r)}function D0(e,n,t){const r=e.all(n),i=t?j0(t):Kd(n),l={},o=[];if(typeof n.checked=="boolean"){const f=r[0];let c;f&&f.type==="element"&&f.tagName==="p"?c=f:(c={type:"element",tagName:"p",properties:{},children:[]},r.unshift(c)),c.children.length>0&&c.children.unshift({type:"text",value:" "}),c.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:n.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let u=-1;for(;++u<r.length;){const f=r[u];(i||u!==0||f.type!=="element"||f.tagName!=="p")&&o.push({type:"text",value:`
`}),f.type==="element"&&f.tagName==="p"&&!i?o.push(...f.children):o.push(f)}const a=r[r.length-1];a&&(i||a.type!=="element"||a.tagName!=="p")&&o.push({type:"text",value:`
`});const s={type:"element",tagName:"li",properties:l,children:o};return e.patch(n,s),e.applyData(n,s)}function j0(e){let n=!1;if(e.type==="list"){n=e.spread||!1;const t=e.children;let r=-1;for(;!n&&++r<t.length;)n=Kd(t[r])}return n}function Kd(e){const n=e.spread;return n??e.children.length>1}function N0(e,n){const t={},r=e.all(n);let i=-1;for(typeof n.start=="number"&&n.start!==1&&(t.start=n.start);++i<r.length;){const o=r[i];if(o.type==="element"&&o.tagName==="li"&&o.properties&&Array.isArray(o.properties.className)&&o.properties.className.includes("task-list-item")){t.className=["contains-task-list"];break}}const l={type:"element",tagName:n.ordered?"ol":"ul",properties:t,children:e.wrap(r,!0)};return e.patch(n,l),e.applyData(n,l)}function F0(e,n){const t={type:"element",tagName:"p",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function M0(e,n){const t={type:"root",children:e.wrap(e.all(n))};return e.patch(n,t),e.applyData(n,t)}function O0(e,n){const t={type:"element",tagName:"strong",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function B0(e,n){const t=e.all(n),r=t.shift(),i=[];if(r){const o={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(n.children[0],o),i.push(o)}if(t.length>0){const o={type:"element",tagName:"tbody",properties:{},children:e.wrap(t,!0)},u=Na(n.children[1]),a=Id(n.children[n.children.length-1]);u&&a&&(o.position={start:u,end:a}),i.push(o)}const l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(n,l),e.applyData(n,l)}function U0(e,n,t){const r=t?t.children:void 0,l=(r?r.indexOf(n):1)===0?"th":"td",o=t&&t.type==="table"?t.align:void 0,u=o?o.length:n.children.length;let a=-1;const s=[];for(;++a<u;){const c=n.children[a],p={},d=o?o[a]:void 0;d&&(p.align=d);let g={type:"element",tagName:l,properties:p,children:[]};c&&(g.children=e.all(c),e.patch(c,g),g=e.applyData(c,g)),s.push(g)}const f={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(n,f),e.applyData(n,f)}function $0(e,n){const t={type:"element",tagName:"td",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}const Dc=9,jc=32;function H0(e){const n=String(e),t=/\r?\n|\r/g;let r=t.exec(n),i=0;const l=[];for(;r;)l.push(Nc(n.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=t.exec(n);return l.push(Nc(n.slice(i),i>0,!1)),l.join("")}function Nc(e,n,t){let r=0,i=e.length;if(n){let l=e.codePointAt(r);for(;l===Dc||l===jc;)r++,l=e.codePointAt(r)}if(t){let l=e.codePointAt(i-1);for(;l===Dc||l===jc;)i--,l=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function V0(e,n){const t={type:"text",value:H0(String(n.value))};return e.patch(n,t),e.applyData(n,t)}function W0(e,n){const t={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(n,t),e.applyData(n,t)}const Q0={blockquote:w0,break:S0,code:C0,delete:E0,emphasis:T0,footnoteReference:z0,heading:P0,html:I0,imageReference:_0,image:b0,inlineCode:L0,linkReference:R0,link:A0,listItem:D0,list:N0,paragraph:F0,root:M0,strong:O0,table:B0,tableCell:$0,tableRow:U0,text:V0,thematicBreak:W0,toml:_i,yaml:_i,definition:_i,footnoteDefinition:_i};function _i(){}const Xd=-1,Ul=0,Nr=1,kl=2,Ha=3,Va=4,Wa=5,Qa=6,Gd=7,Zd=8,Fc=typeof self=="object"?self:globalThis,q0=(e,n)=>{const t=(i,l)=>(e.set(l,i),i),r=i=>{if(e.has(i))return e.get(i);const[l,o]=n[i];switch(l){case Ul:case Xd:return t(o,i);case Nr:{const u=t([],i);for(const a of o)u.push(r(a));return u}case kl:{const u=t({},i);for(const[a,s]of o)u[r(a)]=r(s);return u}case Ha:return t(new Date(o),i);case Va:{const{source:u,flags:a}=o;return t(new RegExp(u,a),i)}case Wa:{const u=t(new Map,i);for(const[a,s]of o)u.set(r(a),r(s));return u}case Qa:{const u=t(new Set,i);for(const a of o)u.add(r(a));return u}case Gd:{const{name:u,message:a}=o;return t(new Fc[u](a),i)}case Zd:return t(BigInt(o),i);case"BigInt":return t(Object(BigInt(o)),i);case"ArrayBuffer":return t(new Uint8Array(o).buffer,o);case"DataView":{const{buffer:u}=new Uint8Array(o);return t(new DataView(u),o)}}return t(new Fc[l](o),i)};return r},Mc=e=>q0(new Map,e)(0),_t="",{toString:Y0}={},{keys:K0}=Object,vr=e=>{const n=typeof e;if(n!=="object"||!e)return[Ul,n];const t=Y0.call(e).slice(8,-1);switch(t){case"Array":return[Nr,_t];case"Object":return[kl,_t];case"Date":return[Ha,_t];case"RegExp":return[Va,_t];case"Map":return[Wa,_t];case"Set":return[Qa,_t];case"DataView":return[Nr,t]}return t.includes("Array")?[Nr,t]:t.includes("Error")?[Gd,t]:[kl,t]},bi=([e,n])=>e===Ul&&(n==="function"||n==="symbol"),X0=(e,n,t,r)=>{const i=(o,u)=>{const a=r.push(o)-1;return t.set(u,a),a},l=o=>{if(t.has(o))return t.get(o);let[u,a]=vr(o);switch(u){case Ul:{let f=o;switch(a){case"bigint":u=Zd,f=o.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+a);f=null;break;case"undefined":return i([Xd],o)}return i([u,f],o)}case Nr:{if(a){let p=o;return a==="DataView"?p=new Uint8Array(o.buffer):a==="ArrayBuffer"&&(p=new Uint8Array(o)),i([a,[...p]],o)}const f=[],c=i([u,f],o);for(const p of o)f.push(l(p));return c}case kl:{if(a)switch(a){case"BigInt":return i([a,o.toString()],o);case"Boolean":case"Number":case"String":return i([a,o.valueOf()],o)}if(n&&"toJSON"in o)return l(o.toJSON());const f=[],c=i([u,f],o);for(const p of K0(o))(e||!bi(vr(o[p])))&&f.push([l(p),l(o[p])]);return c}case Ha:return i([u,o.toISOString()],o);case Va:{const{source:f,flags:c}=o;return i([u,{source:f,flags:c}],o)}case Wa:{const f=[],c=i([u,f],o);for(const[p,d]of o)(e||!(bi(vr(p))||bi(vr(d))))&&f.push([l(p),l(d)]);return c}case Qa:{const f=[],c=i([u,f],o);for(const p of o)(e||!bi(vr(p)))&&f.push(l(p));return c}}const{message:s}=o;return i([u,{name:a,message:s}],o)};return l},Oc=(e,{json:n,lossy:t}={})=>{const r=[];return X0(!(n||t),!!n,new Map,r)(e),r},vl=typeof structuredClone=="function"?(e,n)=>n&&("json"in n||"lossy"in n)?Mc(Oc(e,n)):structuredClone(e):(e,n)=>Mc(Oc(e,n));function G0(e,n){const t=[{type:"text",value:"↩"}];return n>1&&t.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(n)}]}),t}function Z0(e,n){return"Back to reference "+(e+1)+(n>1?"-"+n:"")}function J0(e){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",t=e.options.footnoteBackContent||G0,r=e.options.footnoteBackLabel||Z0,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},u=[];let a=-1;for(;++a<e.footnoteOrder.length;){const s=e.footnoteById.get(e.footnoteOrder[a]);if(!s)continue;const f=e.all(s),c=String(s.identifier).toUpperCase(),p=ar(c.toLowerCase());let d=0;const g=[],v=e.footnoteCounts.get(c);for(;v!==void 0&&++d<=v;){g.length>0&&g.push({type:"text",value:" "});let m=typeof t=="string"?t:t(a,d);typeof m=="string"&&(m={type:"text",value:m}),g.push({type:"element",tagName:"a",properties:{href:"#"+n+"fnref-"+p+(d>1?"-"+d:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(a,d),className:["data-footnote-backref"]},children:Array.isArray(m)?m:[m]})}const E=f[f.length-1];if(E&&E.type==="element"&&E.tagName==="p"){const m=E.children[E.children.length-1];m&&m.type==="text"?m.value+=" ":E.children.push({type:"text",value:" "}),E.children.push(...g)}else f.push(...g);const h={type:"element",tagName:"li",properties:{id:n+"fn-"+p},children:e.wrap(f,!0)};e.patch(s,h),u.push(h)}if(u.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...vl(o),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(u,!0)},{type:"text",value:`
`}]}}const $l=function(e){if(e==null)return rk;if(typeof e=="function")return Hl(e);if(typeof e=="object")return Array.isArray(e)?ek(e):nk(e);if(typeof e=="string")return tk(e);throw new Error("Expected function, string, or object as test")};function ek(e){const n=[];let t=-1;for(;++t<e.length;)n[t]=$l(e[t]);return Hl(r);function r(...i){let l=-1;for(;++l<n.length;)if(n[l].apply(this,i))return!0;return!1}}function nk(e){const n=e;return Hl(t);function t(r){const i=r;let l;for(l in e)if(i[l]!==n[l])return!1;return!0}}function tk(e){return Hl(n);function n(t){return t&&t.type===e}}function Hl(e){return n;function n(t,r,i){return!!(ik(t)&&e.call(this,t,typeof r=="number"?r:void 0,i||void 0))}}function rk(){return!0}function ik(e){return e!==null&&typeof e=="object"&&"type"in e}const Jd=[],lk=!0,Du=!1,ok="skip";function eh(e,n,t,r){let i;typeof n=="function"&&typeof t!="function"?(r=t,t=n):i=n;const l=$l(i),o=r?-1:1;u(e,void 0,[])();function u(a,s,f){const c=a&&typeof a=="object"?a:{};if(typeof c.type=="string"){const d=typeof c.tagName=="string"?c.tagName:typeof c.name=="string"?c.name:void 0;Object.defineProperty(p,"name",{value:"node ("+(a.type+(d?"<"+d+">":""))+")"})}return p;function p(){let d=Jd,g,v,E;if((!n||l(a,s,f[f.length-1]||void 0))&&(d=uk(t(a,f)),d[0]===Du))return d;if("children"in a&&a.children){const h=a;if(h.children&&d[0]!==ok)for(v=(r?h.children.length:-1)+o,E=f.concat(h);v>-1&&v<h.children.length;){const m=h.children[v];if(g=u(m,v,E)(),g[0]===Du)return g;v=typeof g[1]=="number"?g[1]:v+o}}return d}}}function uk(e){return Array.isArray(e)?e:typeof e=="number"?[lk,e]:e==null?Jd:[e]}function qa(e,n,t,r){let i,l,o;typeof n=="function"&&typeof t!="function"?(l=void 0,o=n,i=t):(l=n,o=t,i=r),eh(e,l,u,i);function u(a,s){const f=s[s.length-1],c=f?f.children.indexOf(a):void 0;return o(a,c,f)}}const ju={}.hasOwnProperty,ak={};function sk(e,n){const t=n||ak,r=new Map,i=new Map,l=new Map,o={...Q0,...t.handlers},u={all:s,applyData:fk,definitionById:r,footnoteById:i,footnoteCounts:l,footnoteOrder:[],handlers:o,one:a,options:t,patch:ck,wrap:dk};return qa(e,function(f){if(f.type==="definition"||f.type==="footnoteDefinition"){const c=f.type==="definition"?r:i,p=String(f.identifier).toUpperCase();c.has(p)||c.set(p,f)}}),u;function a(f,c){const p=f.type,d=u.handlers[p];if(ju.call(u.handlers,p)&&d)return d(u,f,c);if(u.options.passThrough&&u.options.passThrough.includes(p)){if("children"in f){const{children:v,...E}=f,h=vl(E);return h.children=u.all(f),h}return vl(f)}return(u.options.unknownHandler||pk)(u,f,c)}function s(f){const c=[];if("children"in f){const p=f.children;let d=-1;for(;++d<p.length;){const g=u.one(p[d],f);if(g){if(d&&p[d-1].type==="break"&&(!Array.isArray(g)&&g.type==="text"&&(g.value=Bc(g.value)),!Array.isArray(g)&&g.type==="element")){const v=g.children[0];v&&v.type==="text"&&(v.value=Bc(v.value))}Array.isArray(g)?c.push(...g):c.push(g)}}}return c}}function ck(e,n){e.position&&(n.position=Zy(e))}function fk(e,n){let t=n;if(e&&e.data){const r=e.data.hName,i=e.data.hChildren,l=e.data.hProperties;if(typeof r=="string")if(t.type==="element")t.tagName=r;else{const o="children"in t?t.children:[t];t={type:"element",tagName:r,properties:{},children:o}}t.type==="element"&&l&&Object.assign(t.properties,vl(l)),"children"in t&&t.children&&i!==null&&i!==void 0&&(t.children=i)}return t}function pk(e,n){const t=n.data||{},r="value"in n&&!(ju.call(t,"hProperties")||ju.call(t,"hChildren"))?{type:"text",value:n.value}:{type:"element",tagName:"div",properties:{},children:e.all(n)};return e.patch(n,r),e.applyData(n,r)}function dk(e,n){const t=[];let r=-1;for(n&&t.push({type:"text",value:`
`});++r<e.length;)r&&t.push({type:"text",value:`
`}),t.push(e[r]);return n&&e.length>0&&t.push({type:"text",value:`
`}),t}function Bc(e){let n=0,t=e.charCodeAt(n);for(;t===9||t===32;)n++,t=e.charCodeAt(n);return e.slice(n)}function Uc(e,n){const t=sk(e,n),r=t.one(e,void 0),i=J0(t),l=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&l.children.push({type:"text",value:`
`},i),l}function hk(e,n){return e&&"run"in e?async function(t,r){const i=Uc(t,{file:r,...n});await e.run(i,r)}:function(t,r){return Uc(t,{file:r,...e||n})}}function $c(e){if(e)throw e}var Qi=Object.prototype.hasOwnProperty,nh=Object.prototype.toString,Hc=Object.defineProperty,Vc=Object.getOwnPropertyDescriptor,Wc=function(n){return typeof Array.isArray=="function"?Array.isArray(n):nh.call(n)==="[object Array]"},Qc=function(n){if(!n||nh.call(n)!=="[object Object]")return!1;var t=Qi.call(n,"constructor"),r=n.constructor&&n.constructor.prototype&&Qi.call(n.constructor.prototype,"isPrototypeOf");if(n.constructor&&!t&&!r)return!1;var i;for(i in n);return typeof i>"u"||Qi.call(n,i)},qc=function(n,t){Hc&&t.name==="__proto__"?Hc(n,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):n[t.name]=t.newValue},Yc=function(n,t){if(t==="__proto__")if(Qi.call(n,t)){if(Vc)return Vc(n,t).value}else return;return n[t]},wo=function e(){var n,t,r,i,l,o,u=arguments[0],a=1,s=arguments.length,f=!1;for(typeof u=="boolean"&&(f=u,u=arguments[1]||{},a=2),(u==null||typeof u!="object"&&typeof u!="function")&&(u={});a<s;++a)if(n=arguments[a],n!=null)for(t in n)r=Yc(u,t),i=Yc(n,t),u!==i&&(f&&i&&(Qc(i)||(l=Wc(i)))?(l?(l=!1,o=r&&Wc(r)?r:[]):o=r&&Qc(r)?r:{},qc(u,{name:t,newValue:e(f,o,i)})):typeof i<"u"&&qc(u,{name:t,newValue:i}));return u};function Nu(e){if(typeof e!="object"||e===null)return!1;const n=Object.getPrototypeOf(e);return(n===null||n===Object.prototype||Object.getPrototypeOf(n)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function mk(){const e=[],n={run:t,use:r};return n;function t(...i){let l=-1;const o=i.pop();if(typeof o!="function")throw new TypeError("Expected function as last argument, not "+o);u(null,...i);function u(a,...s){const f=e[++l];let c=-1;if(a){o(a);return}for(;++c<i.length;)(s[c]===null||s[c]===void 0)&&(s[c]=i[c]);i=s,f?gk(f,u)(...s):o(null,...s)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),n}}function gk(e,n){let t;return r;function r(...o){const u=e.length>o.length;let a;u&&o.push(i);try{a=e.apply(this,o)}catch(s){const f=s;if(u&&t)throw f;return i(f)}u||(a&&a.then&&typeof a.then=="function"?a.then(l,i):a instanceof Error?i(a):l(a))}function i(o,...u){t||(t=!0,n(o,...u))}function l(o){i(null,o)}}const kn={basename:yk,dirname:xk,extname:kk,join:vk,sep:"/"};function yk(e,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');si(e);let t=0,r=-1,i=e.length,l;if(n===void 0||n.length===0||n.length>e.length){for(;i--;)if(e.codePointAt(i)===47){if(l){t=i+1;break}}else r<0&&(l=!0,r=i+1);return r<0?"":e.slice(t,r)}if(n===e)return"";let o=-1,u=n.length-1;for(;i--;)if(e.codePointAt(i)===47){if(l){t=i+1;break}}else o<0&&(l=!0,o=i+1),u>-1&&(e.codePointAt(i)===n.codePointAt(u--)?u<0&&(r=i):(u=-1,r=o));return t===r?r=o:r<0&&(r=e.length),e.slice(t,r)}function xk(e){if(si(e),e.length===0)return".";let n=-1,t=e.length,r;for(;--t;)if(e.codePointAt(t)===47){if(r){n=t;break}}else r||(r=!0);return n<0?e.codePointAt(0)===47?"/":".":n===1&&e.codePointAt(0)===47?"//":e.slice(0,n)}function kk(e){si(e);let n=e.length,t=-1,r=0,i=-1,l=0,o;for(;n--;){const u=e.codePointAt(n);if(u===47){if(o){r=n+1;break}continue}t<0&&(o=!0,t=n+1),u===46?i<0?i=n:l!==1&&(l=1):i>-1&&(l=-1)}return i<0||t<0||l===0||l===1&&i===t-1&&i===r+1?"":e.slice(i,t)}function vk(...e){let n=-1,t;for(;++n<e.length;)si(e[n]),e[n]&&(t=t===void 0?e[n]:t+"/"+e[n]);return t===void 0?".":wk(t)}function wk(e){si(e);const n=e.codePointAt(0)===47;let t=Sk(e,!n);return t.length===0&&!n&&(t="."),t.length>0&&e.codePointAt(e.length-1)===47&&(t+="/"),n?"/"+t:t}function Sk(e,n){let t="",r=0,i=-1,l=0,o=-1,u,a;for(;++o<=e.length;){if(o<e.length)u=e.codePointAt(o);else{if(u===47)break;u=47}if(u===47){if(!(i===o-1||l===1))if(i!==o-1&&l===2){if(t.length<2||r!==2||t.codePointAt(t.length-1)!==46||t.codePointAt(t.length-2)!==46){if(t.length>2){if(a=t.lastIndexOf("/"),a!==t.length-1){a<0?(t="",r=0):(t=t.slice(0,a),r=t.length-1-t.lastIndexOf("/")),i=o,l=0;continue}}else if(t.length>0){t="",r=0,i=o,l=0;continue}}n&&(t=t.length>0?t+"/..":"..",r=2)}else t.length>0?t+="/"+e.slice(i+1,o):t=e.slice(i+1,o),r=o-i-1;i=o,l=0}else u===46&&l>-1?l++:l=-1}return t}function si(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const Ck={cwd:Ek};function Ek(){return"/"}function Fu(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function Tk(e){if(typeof e=="string")e=new URL(e);else if(!Fu(e)){const n=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw n.code="ERR_INVALID_ARG_TYPE",n}if(e.protocol!=="file:"){const n=new TypeError("The URL must be of scheme file");throw n.code="ERR_INVALID_URL_SCHEME",n}return zk(e)}function zk(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const n=e.pathname;let t=-1;for(;++t<n.length;)if(n.codePointAt(t)===37&&n.codePointAt(t+1)===50){const r=n.codePointAt(t+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(n)}const So=["history","path","basename","stem","extname","dirname"];class th{constructor(n){let t;n?Fu(n)?t={path:n}:typeof n=="string"||Pk(n)?t={value:n}:t=n:t={},this.cwd="cwd"in t?"":Ck.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<So.length;){const l=So[r];l in t&&t[l]!==void 0&&t[l]!==null&&(this[l]=l==="history"?[...t[l]]:t[l])}let i;for(i in t)So.includes(i)||(this[i]=t[i])}get basename(){return typeof this.path=="string"?kn.basename(this.path):void 0}set basename(n){Eo(n,"basename"),Co(n,"basename"),this.path=kn.join(this.dirname||"",n)}get dirname(){return typeof this.path=="string"?kn.dirname(this.path):void 0}set dirname(n){Kc(this.basename,"dirname"),this.path=kn.join(n||"",this.basename)}get extname(){return typeof this.path=="string"?kn.extname(this.path):void 0}set extname(n){if(Co(n,"extname"),Kc(this.dirname,"extname"),n){if(n.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(n.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=kn.join(this.dirname,this.stem+(n||""))}get path(){return this.history[this.history.length-1]}set path(n){Fu(n)&&(n=Tk(n)),Eo(n,"path"),this.path!==n&&this.history.push(n)}get stem(){return typeof this.path=="string"?kn.basename(this.path,this.extname):void 0}set stem(n){Eo(n,"stem"),Co(n,"stem"),this.path=kn.join(this.dirname||"",n+(this.extname||""))}fail(n,t,r){const i=this.message(n,t,r);throw i.fatal=!0,i}info(n,t,r){const i=this.message(n,t,r);return i.fatal=void 0,i}message(n,t,r){const i=new be(n,t,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(n){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(n||void 0).decode(this.value)}}function Co(e,n){if(e&&e.includes(kn.sep))throw new Error("`"+n+"` cannot be a path: did not expect `"+kn.sep+"`")}function Eo(e,n){if(!e)throw new Error("`"+n+"` cannot be empty")}function Kc(e,n){if(!e)throw new Error("Setting `"+n+"` requires `path` to be set too")}function Pk(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Ik=function(e){const r=this.constructor.prototype,i=r[e],l=function(){return i.apply(l,arguments)};return Object.setPrototypeOf(l,r),l},_k={}.hasOwnProperty;class Ya extends Ik{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=mk()}copy(){const n=new Ya;let t=-1;for(;++t<this.attachers.length;){const r=this.attachers[t];n.use(...r)}return n.data(wo(!0,{},this.namespace)),n}data(n,t){return typeof n=="string"?arguments.length===2?(Po("data",this.frozen),this.namespace[n]=t,this):_k.call(this.namespace,n)&&this.namespace[n]||void 0:n?(Po("data",this.frozen),this.namespace=n,this):this.namespace}freeze(){if(this.frozen)return this;const n=this;for(;++this.freezeIndex<this.attachers.length;){const[t,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const i=t.call(n,...r);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(n){this.freeze();const t=Li(n),r=this.parser||this.Parser;return To("parse",r),r(String(t),t)}process(n,t){const r=this;return this.freeze(),To("process",this.parser||this.Parser),zo("process",this.compiler||this.Compiler),t?i(void 0,t):new Promise(i);function i(l,o){const u=Li(n),a=r.parse(u);r.run(a,u,function(f,c,p){if(f||!c||!p)return s(f);const d=c,g=r.stringify(d,p);Rk(g)?p.value=g:p.result=g,s(f,p)});function s(f,c){f||!c?o(f):l?l(c):t(void 0,c)}}}processSync(n){let t=!1,r;return this.freeze(),To("processSync",this.parser||this.Parser),zo("processSync",this.compiler||this.Compiler),this.process(n,i),Gc("processSync","process",t),r;function i(l,o){t=!0,$c(l),r=o}}run(n,t,r){Xc(n),this.freeze();const i=this.transformers;return!r&&typeof t=="function"&&(r=t,t=void 0),r?l(void 0,r):new Promise(l);function l(o,u){const a=Li(t);i.run(n,a,s);function s(f,c,p){const d=c||n;f?u(f):o?o(d):r(void 0,d,p)}}}runSync(n,t){let r=!1,i;return this.run(n,t,l),Gc("runSync","run",r),i;function l(o,u){$c(o),i=u,r=!0}}stringify(n,t){this.freeze();const r=Li(t),i=this.compiler||this.Compiler;return zo("stringify",i),Xc(n),i(n,r)}use(n,...t){const r=this.attachers,i=this.namespace;if(Po("use",this.frozen),n!=null)if(typeof n=="function")a(n,t);else if(typeof n=="object")Array.isArray(n)?u(n):o(n);else throw new TypeError("Expected usable value, not `"+n+"`");return this;function l(s){if(typeof s=="function")a(s,[]);else if(typeof s=="object")if(Array.isArray(s)){const[f,...c]=s;a(f,c)}else o(s);else throw new TypeError("Expected usable value, not `"+s+"`")}function o(s){if(!("plugins"in s)&&!("settings"in s))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");u(s.plugins),s.settings&&(i.settings=wo(!0,i.settings,s.settings))}function u(s){let f=-1;if(s!=null)if(Array.isArray(s))for(;++f<s.length;){const c=s[f];l(c)}else throw new TypeError("Expected a list of plugins, not `"+s+"`")}function a(s,f){let c=-1,p=-1;for(;++c<r.length;)if(r[c][0]===s){p=c;break}if(p===-1)r.push([s,...f]);else if(f.length>0){let[d,...g]=f;const v=r[p][1];Nu(v)&&Nu(d)&&(d=wo(!0,v,d)),r[p]=[s,d,...g]}}}}const bk=new Ya().freeze();function To(e,n){if(typeof n!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function zo(e,n){if(typeof n!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function Po(e,n){if(n)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Xc(e){if(!Nu(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Gc(e,n,t){if(!t)throw new Error("`"+e+"` finished async. Use `"+n+"` instead")}function Li(e){return Lk(e)?e:new th(e)}function Lk(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function Rk(e){return typeof e=="string"||Ak(e)}function Ak(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Dk="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Zc=[],Jc={allowDangerousHtml:!0},jk=/^(https?|ircs?|mailto|xmpp)$/i,Nk=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function Fk(e){const n=Mk(e),t=Ok(e);return Bk(n.runSync(n.parse(t),t),e)}function Mk(e){const n=e.rehypePlugins||Zc,t=e.remarkPlugins||Zc,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Jc}:Jc;return bk().use(v0).use(t).use(hk,r).use(n)}function Ok(e){const n=e.children||"",t=new th;return typeof n=="string"&&(t.value=n),t}function Bk(e,n){const t=n.allowedElements,r=n.allowElement,i=n.components,l=n.disallowedElements,o=n.skipHtml,u=n.unwrapDisallowed,a=n.urlTransform||Uk;for(const f of Nk)Object.hasOwn(n,f.from)&&(""+f.from+(f.to?"use `"+f.to+"` instead":"remove it")+Dk+f.id,void 0);return qa(e,s),r1(e,{Fragment:w.Fragment,components:i,ignoreInvalidStyle:!0,jsx:w.jsx,jsxs:w.jsxs,passKeys:!0,passNode:!0});function s(f,c,p){if(f.type==="raw"&&p&&typeof c=="number")return o?p.children.splice(c,1):p.children[c]={type:"text",value:f.value},c;if(f.type==="element"){let d;for(d in xo)if(Object.hasOwn(xo,d)&&Object.hasOwn(f.properties,d)){const g=f.properties[d],v=xo[d];(v===null||v.includes(f.tagName))&&(f.properties[d]=a(String(g||""),d,f))}}if(f.type==="element"){let d=t?!t.includes(f.tagName):l?l.includes(f.tagName):!1;if(!d&&r&&typeof c=="number"&&(d=!r(f,c,p)),d&&p&&typeof c=="number")return u&&f.children?p.children.splice(c,1,...f.children):p.children.splice(c,1),c}}}function Uk(e){const n=e.indexOf(":"),t=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return n===-1||i!==-1&&n>i||t!==-1&&n>t||r!==-1&&n>r||jk.test(e.slice(0,n))?e:""}function ef(e,n){const t=String(e);if(typeof n!="string")throw new TypeError("Expected character");let r=0,i=t.indexOf(n);for(;i!==-1;)r++,i=t.indexOf(n,i+n.length);return r}function $k(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function Hk(e,n,t){const i=$l((t||{}).ignore||[]),l=Vk(n);let o=-1;for(;++o<l.length;)eh(e,"text",u);function u(s,f){let c=-1,p;for(;++c<f.length;){const d=f[c],g=p?p.children:void 0;if(i(d,g?g.indexOf(d):void 0,p))return;p=d}if(p)return a(s,f)}function a(s,f){const c=f[f.length-1],p=l[o][0],d=l[o][1];let g=0;const E=c.children.indexOf(s);let h=!1,m=[];p.lastIndex=0;let y=p.exec(s.value);for(;y;){const T=y.index,P={index:y.index,input:y.input,stack:[...f,s]};let S=d(...y,P);if(typeof S=="string"&&(S=S.length>0?{type:"text",value:S}:void 0),S===!1?p.lastIndex=T+1:(g!==T&&m.push({type:"text",value:s.value.slice(g,T)}),Array.isArray(S)?m.push(...S):S&&m.push(S),g=T+y[0].length,h=!0),!p.global)break;y=p.exec(s.value)}return h?(g<s.value.length&&m.push({type:"text",value:s.value.slice(g)}),c.children.splice(E,1,...m)):m=[s],E+m.length}}function Vk(e){const n=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const t=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<t.length;){const i=t[r];n.push([Wk(i[0]),Qk(i[1])])}return n}function Wk(e){return typeof e=="string"?new RegExp($k(e),"g"):e}function Qk(e){return typeof e=="function"?e:function(){return e}}const Io="phrasing",_o=["autolink","link","image","label"];function qk(){return{transforms:[ev],enter:{literalAutolink:Kk,literalAutolinkEmail:bo,literalAutolinkHttp:bo,literalAutolinkWww:bo},exit:{literalAutolink:Jk,literalAutolinkEmail:Zk,literalAutolinkHttp:Xk,literalAutolinkWww:Gk}}}function Yk(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:Io,notInConstruct:_o},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:Io,notInConstruct:_o},{character:":",before:"[ps]",after:"\\/",inConstruct:Io,notInConstruct:_o}]}}function Kk(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function bo(e){this.config.enter.autolinkProtocol.call(this,e)}function Xk(e){this.config.exit.autolinkProtocol.call(this,e)}function Gk(e){this.config.exit.data.call(this,e);const n=this.stack[this.stack.length-1];n.type,n.url="http://"+this.sliceSerialize(e)}function Zk(e){this.config.exit.autolinkEmail.call(this,e)}function Jk(e){this.exit(e)}function ev(e){Hk(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,nv],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),tv]],{ignore:["link","linkReference"]})}function nv(e,n,t,r,i){let l="";if(!rh(i)||(/^w/i.test(n)&&(t=n+t,n="",l="http://"),!rv(t)))return!1;const o=iv(t+r);if(!o[0])return!1;const u={type:"link",title:null,url:l+n+o[0],children:[{type:"text",value:n+o[0]}]};return o[1]?[u,{type:"text",value:o[1]}]:u}function tv(e,n,t,r){return!rh(r,!0)||/[-\d_]$/.test(t)?!1:{type:"link",title:null,url:"mailto:"+n+"@"+t,children:[{type:"text",value:n+"@"+t}]}}function rv(e){const n=e.split(".");return!(n.length<2||n[n.length-1]&&(/_/.test(n[n.length-1])||!/[a-zA-Z\d]/.test(n[n.length-1]))||n[n.length-2]&&(/_/.test(n[n.length-2])||!/[a-zA-Z\d]/.test(n[n.length-2])))}function iv(e){const n=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!n)return[e,void 0];e=e.slice(0,n.index);let t=n[0],r=t.indexOf(")");const i=ef(e,"(");let l=ef(e,")");for(;r!==-1&&i>l;)e+=t.slice(0,r+1),t=t.slice(r+1),r=t.indexOf(")"),l++;return[e,t]}function rh(e,n){const t=e.input.charCodeAt(e.index-1);return(e.index===0||wt(t)||Ol(t))&&(!n||t!==47)}ih.peek=dv;function lv(){this.buffer()}function ov(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function uv(){this.buffer()}function av(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function sv(e){const n=this.resume(),t=this.stack[this.stack.length-1];t.type,t.identifier=hn(this.sliceSerialize(e)).toLowerCase(),t.label=n}function cv(e){this.exit(e)}function fv(e){const n=this.resume(),t=this.stack[this.stack.length-1];t.type,t.identifier=hn(this.sliceSerialize(e)).toLowerCase(),t.label=n}function pv(e){this.exit(e)}function dv(){return"["}function ih(e,n,t,r){const i=t.createTracker(r);let l=i.move("[^");const o=t.enter("footnoteReference"),u=t.enter("reference");return l+=i.move(t.safe(t.associationId(e),{after:"]",before:l})),u(),o(),l+=i.move("]"),l}function hv(){return{enter:{gfmFootnoteCallString:lv,gfmFootnoteCall:ov,gfmFootnoteDefinitionLabelString:uv,gfmFootnoteDefinition:av},exit:{gfmFootnoteCallString:sv,gfmFootnoteCall:cv,gfmFootnoteDefinitionLabelString:fv,gfmFootnoteDefinition:pv}}}function mv(e){let n=!1;return e&&e.firstLineBlank&&(n=!0),{handlers:{footnoteDefinition:t,footnoteReference:ih},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function t(r,i,l,o){const u=l.createTracker(o);let a=u.move("[^");const s=l.enter("footnoteDefinition"),f=l.enter("label");return a+=u.move(l.safe(l.associationId(r),{before:a,after:"]"})),f(),a+=u.move("]:"),r.children&&r.children.length>0&&(u.shift(4),a+=u.move((n?`
`:" ")+l.indentLines(l.containerFlow(r,u.current()),n?lh:gv))),s(),a}}function gv(e,n,t){return n===0?e:lh(e,n,t)}function lh(e,n,t){return(t?"":"    ")+e}const yv=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];oh.peek=Sv;function xv(){return{canContainEols:["delete"],enter:{strikethrough:vv},exit:{strikethrough:wv}}}function kv(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:yv}],handlers:{delete:oh}}}function vv(e){this.enter({type:"delete",children:[]},e)}function wv(e){this.exit(e)}function oh(e,n,t,r){const i=t.createTracker(r),l=t.enter("strikethrough");let o=i.move("~~");return o+=t.containerPhrasing(e,{...i.current(),before:o,after:"~"}),o+=i.move("~~"),l(),o}function Sv(){return"~"}function Cv(e){return e.length}function Ev(e,n){const t=n||{},r=(t.align||[]).concat(),i=t.stringLength||Cv,l=[],o=[],u=[],a=[];let s=0,f=-1;for(;++f<e.length;){const v=[],E=[];let h=-1;for(e[f].length>s&&(s=e[f].length);++h<e[f].length;){const m=Tv(e[f][h]);if(t.alignDelimiters!==!1){const y=i(m);E[h]=y,(a[h]===void 0||y>a[h])&&(a[h]=y)}v.push(m)}o[f]=v,u[f]=E}let c=-1;if(typeof r=="object"&&"length"in r)for(;++c<s;)l[c]=nf(r[c]);else{const v=nf(r);for(;++c<s;)l[c]=v}c=-1;const p=[],d=[];for(;++c<s;){const v=l[c];let E="",h="";v===99?(E=":",h=":"):v===108?E=":":v===114&&(h=":");let m=t.alignDelimiters===!1?1:Math.max(1,a[c]-E.length-h.length);const y=E+"-".repeat(m)+h;t.alignDelimiters!==!1&&(m=E.length+m+h.length,m>a[c]&&(a[c]=m),d[c]=m),p[c]=y}o.splice(1,0,p),u.splice(1,0,d),f=-1;const g=[];for(;++f<o.length;){const v=o[f],E=u[f];c=-1;const h=[];for(;++c<s;){const m=v[c]||"";let y="",T="";if(t.alignDelimiters!==!1){const P=a[c]-(E[c]||0),S=l[c];S===114?y=" ".repeat(P):S===99?P%2?(y=" ".repeat(P/2+.5),T=" ".repeat(P/2-.5)):(y=" ".repeat(P/2),T=y):T=" ".repeat(P)}t.delimiterStart!==!1&&!c&&h.push("|"),t.padding!==!1&&!(t.alignDelimiters===!1&&m==="")&&(t.delimiterStart!==!1||c)&&h.push(" "),t.alignDelimiters!==!1&&h.push(y),h.push(m),t.alignDelimiters!==!1&&h.push(T),t.padding!==!1&&h.push(" "),(t.delimiterEnd!==!1||c!==s-1)&&h.push("|")}g.push(t.delimiterEnd===!1?h.join("").replace(/ +$/,""):h.join(""))}return g.join(`
`)}function Tv(e){return e==null?"":String(e)}function nf(e){const n=typeof e=="string"?e.codePointAt(0):0;return n===67||n===99?99:n===76||n===108?108:n===82||n===114?114:0}function zv(e,n,t,r){const i=t.enter("blockquote"),l=t.createTracker(r);l.move("> "),l.shift(2);const o=t.indentLines(t.containerFlow(e,l.current()),Pv);return i(),o}function Pv(e,n,t){return">"+(t?"":" ")+e}function Iv(e,n){return tf(e,n.inConstruct,!0)&&!tf(e,n.notInConstruct,!1)}function tf(e,n,t){if(typeof n=="string"&&(n=[n]),!n||n.length===0)return t;let r=-1;for(;++r<n.length;)if(e.includes(n[r]))return!0;return!1}function rf(e,n,t,r){let i=-1;for(;++i<t.unsafe.length;)if(t.unsafe[i].character===`
`&&Iv(t.stack,t.unsafe[i]))return/[ \t]/.test(r.before)?"":" ";return`\\
`}function _v(e,n){const t=String(e);let r=t.indexOf(n),i=r,l=0,o=0;if(typeof n!="string")throw new TypeError("Expected substring");for(;r!==-1;)r===i?++l>o&&(o=l):l=1,i=r+n.length,r=t.indexOf(n,i);return o}function bv(e,n){return!!(n.options.fences===!1&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}function Lv(e){const n=e.options.fence||"`";if(n!=="`"&&n!=="~")throw new Error("Cannot serialize code with `"+n+"` for `options.fence`, expected `` ` `` or `~`");return n}function Rv(e,n,t,r){const i=Lv(t),l=e.value||"",o=i==="`"?"GraveAccent":"Tilde";if(bv(e,t)){const c=t.enter("codeIndented"),p=t.indentLines(l,Av);return c(),p}const u=t.createTracker(r),a=i.repeat(Math.max(_v(l,i)+1,3)),s=t.enter("codeFenced");let f=u.move(a);if(e.lang){const c=t.enter(`codeFencedLang${o}`);f+=u.move(t.safe(e.lang,{before:f,after:" ",encode:["`"],...u.current()})),c()}if(e.lang&&e.meta){const c=t.enter(`codeFencedMeta${o}`);f+=u.move(" "),f+=u.move(t.safe(e.meta,{before:f,after:`
`,encode:["`"],...u.current()})),c()}return f+=u.move(`
`),l&&(f+=u.move(l+`
`)),f+=u.move(a),s(),f}function Av(e,n,t){return(t?"":"    ")+e}function Ka(e){const n=e.options.quote||'"';if(n!=='"'&&n!=="'")throw new Error("Cannot serialize title with `"+n+"` for `options.quote`, expected `\"`, or `'`");return n}function Dv(e,n,t,r){const i=Ka(t),l=i==='"'?"Quote":"Apostrophe",o=t.enter("definition");let u=t.enter("label");const a=t.createTracker(r);let s=a.move("[");return s+=a.move(t.safe(t.associationId(e),{before:s,after:"]",...a.current()})),s+=a.move("]: "),u(),!e.url||/[\0- \u007F]/.test(e.url)?(u=t.enter("destinationLiteral"),s+=a.move("<"),s+=a.move(t.safe(e.url,{before:s,after:">",...a.current()})),s+=a.move(">")):(u=t.enter("destinationRaw"),s+=a.move(t.safe(e.url,{before:s,after:e.title?" ":`
`,...a.current()}))),u(),e.title&&(u=t.enter(`title${l}`),s+=a.move(" "+i),s+=a.move(t.safe(e.title,{before:s,after:i,...a.current()})),s+=a.move(i),u()),o(),s}function jv(e){const n=e.options.emphasis||"*";if(n!=="*"&&n!=="_")throw new Error("Cannot serialize emphasis with `"+n+"` for `options.emphasis`, expected `*`, or `_`");return n}function ni(e){return"&#x"+e.toString(16).toUpperCase()+";"}function wl(e,n,t){const r=rr(e),i=rr(n);return r===void 0?i===void 0?t==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:r===1?i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}uh.peek=Nv;function uh(e,n,t,r){const i=jv(t),l=t.enter("emphasis"),o=t.createTracker(r),u=o.move(i);let a=o.move(t.containerPhrasing(e,{after:i,before:u,...o.current()}));const s=a.charCodeAt(0),f=wl(r.before.charCodeAt(r.before.length-1),s,i);f.inside&&(a=ni(s)+a.slice(1));const c=a.charCodeAt(a.length-1),p=wl(r.after.charCodeAt(0),c,i);p.inside&&(a=a.slice(0,-1)+ni(c));const d=o.move(i);return l(),t.attentionEncodeSurroundingInfo={after:p.outside,before:f.outside},u+a+d}function Nv(e,n,t){return t.options.emphasis||"*"}function Fv(e,n){let t=!1;return qa(e,function(r){if("value"in r&&/\r?\n|\r/.test(r.value)||r.type==="break")return t=!0,Du}),!!((!e.depth||e.depth<3)&&Ba(e)&&(n.options.setext||t))}function Mv(e,n,t,r){const i=Math.max(Math.min(6,e.depth||1),1),l=t.createTracker(r);if(Fv(e,t)){const f=t.enter("headingSetext"),c=t.enter("phrasing"),p=t.containerPhrasing(e,{...l.current(),before:`
`,after:`
`});return c(),f(),p+`
`+(i===1?"=":"-").repeat(p.length-(Math.max(p.lastIndexOf("\r"),p.lastIndexOf(`
`))+1))}const o="#".repeat(i),u=t.enter("headingAtx"),a=t.enter("phrasing");l.move(o+" ");let s=t.containerPhrasing(e,{before:"# ",after:`
`,...l.current()});return/^[\t ]/.test(s)&&(s=ni(s.charCodeAt(0))+s.slice(1)),s=s?o+" "+s:o,t.options.closeAtx&&(s+=" "+o),a(),u(),s}ah.peek=Ov;function ah(e){return e.value||""}function Ov(){return"<"}sh.peek=Bv;function sh(e,n,t,r){const i=Ka(t),l=i==='"'?"Quote":"Apostrophe",o=t.enter("image");let u=t.enter("label");const a=t.createTracker(r);let s=a.move("![");return s+=a.move(t.safe(e.alt,{before:s,after:"]",...a.current()})),s+=a.move("]("),u(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(u=t.enter("destinationLiteral"),s+=a.move("<"),s+=a.move(t.safe(e.url,{before:s,after:">",...a.current()})),s+=a.move(">")):(u=t.enter("destinationRaw"),s+=a.move(t.safe(e.url,{before:s,after:e.title?" ":")",...a.current()}))),u(),e.title&&(u=t.enter(`title${l}`),s+=a.move(" "+i),s+=a.move(t.safe(e.title,{before:s,after:i,...a.current()})),s+=a.move(i),u()),s+=a.move(")"),o(),s}function Bv(){return"!"}ch.peek=Uv;function ch(e,n,t,r){const i=e.referenceType,l=t.enter("imageReference");let o=t.enter("label");const u=t.createTracker(r);let a=u.move("![");const s=t.safe(e.alt,{before:a,after:"]",...u.current()});a+=u.move(s+"]["),o();const f=t.stack;t.stack=[],o=t.enter("reference");const c=t.safe(t.associationId(e),{before:a,after:"]",...u.current()});return o(),t.stack=f,l(),i==="full"||!s||s!==c?a+=u.move(c+"]"):i==="shortcut"?a=a.slice(0,-1):a+=u.move("]"),a}function Uv(){return"!"}fh.peek=$v;function fh(e,n,t){let r=e.value||"",i="`",l=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++l<t.unsafe.length;){const o=t.unsafe[l],u=t.compilePattern(o);let a;if(o.atBreak)for(;a=u.exec(r);){let s=a.index;r.charCodeAt(s)===10&&r.charCodeAt(s-1)===13&&s--,r=r.slice(0,s)+" "+r.slice(a.index+1)}}return i+r+i}function $v(){return"`"}function ph(e,n){const t=Ba(e);return!!(!n.options.resourceLink&&e.url&&!e.title&&e.children&&e.children.length===1&&e.children[0].type==="text"&&(t===e.url||"mailto:"+t===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}dh.peek=Hv;function dh(e,n,t,r){const i=Ka(t),l=i==='"'?"Quote":"Apostrophe",o=t.createTracker(r);let u,a;if(ph(e,t)){const f=t.stack;t.stack=[],u=t.enter("autolink");let c=o.move("<");return c+=o.move(t.containerPhrasing(e,{before:c,after:">",...o.current()})),c+=o.move(">"),u(),t.stack=f,c}u=t.enter("link"),a=t.enter("label");let s=o.move("[");return s+=o.move(t.containerPhrasing(e,{before:s,after:"](",...o.current()})),s+=o.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=t.enter("destinationLiteral"),s+=o.move("<"),s+=o.move(t.safe(e.url,{before:s,after:">",...o.current()})),s+=o.move(">")):(a=t.enter("destinationRaw"),s+=o.move(t.safe(e.url,{before:s,after:e.title?" ":")",...o.current()}))),a(),e.title&&(a=t.enter(`title${l}`),s+=o.move(" "+i),s+=o.move(t.safe(e.title,{before:s,after:i,...o.current()})),s+=o.move(i),a()),s+=o.move(")"),u(),s}function Hv(e,n,t){return ph(e,t)?"<":"["}hh.peek=Vv;function hh(e,n,t,r){const i=e.referenceType,l=t.enter("linkReference");let o=t.enter("label");const u=t.createTracker(r);let a=u.move("[");const s=t.containerPhrasing(e,{before:a,after:"]",...u.current()});a+=u.move(s+"]["),o();const f=t.stack;t.stack=[],o=t.enter("reference");const c=t.safe(t.associationId(e),{before:a,after:"]",...u.current()});return o(),t.stack=f,l(),i==="full"||!s||s!==c?a+=u.move(c+"]"):i==="shortcut"?a=a.slice(0,-1):a+=u.move("]"),a}function Vv(){return"["}function Xa(e){const n=e.options.bullet||"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bullet`, expected `*`, `+`, or `-`");return n}function Wv(e){const n=Xa(e),t=e.options.bulletOther;if(!t)return n==="*"?"-":"*";if(t!=="*"&&t!=="+"&&t!=="-")throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(t===n)throw new Error("Expected `bullet` (`"+n+"`) and `bulletOther` (`"+t+"`) to be different");return t}function Qv(e){const n=e.options.bulletOrdered||".";if(n!=="."&&n!==")")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOrdered`, expected `.` or `)`");return n}function mh(e){const n=e.options.rule||"*";if(n!=="*"&&n!=="-"&&n!=="_")throw new Error("Cannot serialize rules with `"+n+"` for `options.rule`, expected `*`, `-`, or `_`");return n}function qv(e,n,t,r){const i=t.enter("list"),l=t.bulletCurrent;let o=e.ordered?Qv(t):Xa(t);const u=e.ordered?o==="."?")":".":Wv(t);let a=n&&t.bulletLastUsed?o===t.bulletLastUsed:!1;if(!e.ordered){const f=e.children?e.children[0]:void 0;if((o==="*"||o==="-")&&f&&(!f.children||!f.children[0])&&t.stack[t.stack.length-1]==="list"&&t.stack[t.stack.length-2]==="listItem"&&t.stack[t.stack.length-3]==="list"&&t.stack[t.stack.length-4]==="listItem"&&t.indexStack[t.indexStack.length-1]===0&&t.indexStack[t.indexStack.length-2]===0&&t.indexStack[t.indexStack.length-3]===0&&(a=!0),mh(t)===o&&f){let c=-1;for(;++c<e.children.length;){const p=e.children[c];if(p&&p.type==="listItem"&&p.children&&p.children[0]&&p.children[0].type==="thematicBreak"){a=!0;break}}}}a&&(o=u),t.bulletCurrent=o;const s=t.containerFlow(e,r);return t.bulletLastUsed=o,t.bulletCurrent=l,i(),s}function Yv(e){const n=e.options.listItemIndent||"one";if(n!=="tab"&&n!=="one"&&n!=="mixed")throw new Error("Cannot serialize items with `"+n+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return n}function Kv(e,n,t,r){const i=Yv(t);let l=t.bulletCurrent||Xa(t);n&&n.type==="list"&&n.ordered&&(l=(typeof n.start=="number"&&n.start>-1?n.start:1)+(t.options.incrementListMarker===!1?0:n.children.indexOf(e))+l);let o=l.length+1;(i==="tab"||i==="mixed"&&(n&&n.type==="list"&&n.spread||e.spread))&&(o=Math.ceil(o/4)*4);const u=t.createTracker(r);u.move(l+" ".repeat(o-l.length)),u.shift(o);const a=t.enter("listItem"),s=t.indentLines(t.containerFlow(e,u.current()),f);return a(),s;function f(c,p,d){return p?(d?"":" ".repeat(o))+c:(d?l:l+" ".repeat(o-l.length))+c}}function Xv(e,n,t,r){const i=t.enter("paragraph"),l=t.enter("phrasing"),o=t.containerPhrasing(e,r);return l(),i(),o}const Gv=$l(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function Zv(e,n,t,r){return(e.children.some(function(o){return Gv(o)})?t.containerPhrasing:t.containerFlow).call(t,e,r)}function Jv(e){const n=e.options.strong||"*";if(n!=="*"&&n!=="_")throw new Error("Cannot serialize strong with `"+n+"` for `options.strong`, expected `*`, or `_`");return n}gh.peek=ew;function gh(e,n,t,r){const i=Jv(t),l=t.enter("strong"),o=t.createTracker(r),u=o.move(i+i);let a=o.move(t.containerPhrasing(e,{after:i,before:u,...o.current()}));const s=a.charCodeAt(0),f=wl(r.before.charCodeAt(r.before.length-1),s,i);f.inside&&(a=ni(s)+a.slice(1));const c=a.charCodeAt(a.length-1),p=wl(r.after.charCodeAt(0),c,i);p.inside&&(a=a.slice(0,-1)+ni(c));const d=o.move(i+i);return l(),t.attentionEncodeSurroundingInfo={after:p.outside,before:f.outside},u+a+d}function ew(e,n,t){return t.options.strong||"*"}function nw(e,n,t,r){return t.safe(e.value,r)}function tw(e){const n=e.options.ruleRepetition||3;if(n<3)throw new Error("Cannot serialize rules with repetition `"+n+"` for `options.ruleRepetition`, expected `3` or more");return n}function rw(e,n,t){const r=(mh(t)+(t.options.ruleSpaces?" ":"")).repeat(tw(t));return t.options.ruleSpaces?r.slice(0,-1):r}const yh={blockquote:zv,break:rf,code:Rv,definition:Dv,emphasis:uh,hardBreak:rf,heading:Mv,html:ah,image:sh,imageReference:ch,inlineCode:fh,link:dh,linkReference:hh,list:qv,listItem:Kv,paragraph:Xv,root:Zv,strong:gh,text:nw,thematicBreak:rw};function iw(){return{enter:{table:lw,tableData:lf,tableHeader:lf,tableRow:uw},exit:{codeText:aw,table:ow,tableData:Lo,tableHeader:Lo,tableRow:Lo}}}function lw(e){const n=e._align;this.enter({type:"table",align:n.map(function(t){return t==="none"?null:t}),children:[]},e),this.data.inTable=!0}function ow(e){this.exit(e),this.data.inTable=void 0}function uw(e){this.enter({type:"tableRow",children:[]},e)}function Lo(e){this.exit(e)}function lf(e){this.enter({type:"tableCell",children:[]},e)}function aw(e){let n=this.resume();this.data.inTable&&(n=n.replace(/\\([\\|])/g,sw));const t=this.stack[this.stack.length-1];t.type,t.value=n,this.exit(e)}function sw(e,n){return n==="|"?n:e}function cw(e){const n=e||{},t=n.tableCellPadding,r=n.tablePipeAlign,i=n.stringLength,l=t?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:p,table:o,tableCell:a,tableRow:u}};function o(d,g,v,E){return s(f(d,v,E),d.align)}function u(d,g,v,E){const h=c(d,v,E),m=s([h]);return m.slice(0,m.indexOf(`
`))}function a(d,g,v,E){const h=v.enter("tableCell"),m=v.enter("phrasing"),y=v.containerPhrasing(d,{...E,before:l,after:l});return m(),h(),y}function s(d,g){return Ev(d,{align:g,alignDelimiters:r,padding:t,stringLength:i})}function f(d,g,v){const E=d.children;let h=-1;const m=[],y=g.enter("table");for(;++h<E.length;)m[h]=c(E[h],g,v);return y(),m}function c(d,g,v){const E=d.children;let h=-1;const m=[],y=g.enter("tableRow");for(;++h<E.length;)m[h]=a(E[h],d,g,v);return y(),m}function p(d,g,v){let E=yh.inlineCode(d,g,v);return v.stack.includes("tableCell")&&(E=E.replace(/\|/g,"\\$&")),E}}function fw(){return{exit:{taskListCheckValueChecked:of,taskListCheckValueUnchecked:of,paragraph:dw}}}function pw(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:hw}}}function of(e){const n=this.stack[this.stack.length-2];n.type,n.checked=e.type==="taskListCheckValueChecked"}function dw(e){const n=this.stack[this.stack.length-2];if(n&&n.type==="listItem"&&typeof n.checked=="boolean"){const t=this.stack[this.stack.length-1];t.type;const r=t.children[0];if(r&&r.type==="text"){const i=n.children;let l=-1,o;for(;++l<i.length;){const u=i[l];if(u.type==="paragraph"){o=u;break}}o===t&&(r.value=r.value.slice(1),r.value.length===0?t.children.shift():t.position&&r.position&&typeof r.position.start.offset=="number"&&(r.position.start.column++,r.position.start.offset++,t.position.start=Object.assign({},r.position.start)))}}this.exit(e)}function hw(e,n,t,r){const i=e.children[0],l=typeof e.checked=="boolean"&&i&&i.type==="paragraph",o="["+(e.checked?"x":" ")+"] ",u=t.createTracker(r);l&&u.move(o);let a=yh.listItem(e,n,t,{...r,...u.current()});return l&&(a=a.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,s)),a;function s(f){return f+o}}function mw(){return[qk(),hv(),xv(),iw(),fw()]}function gw(e){return{extensions:[Yk(),mv(e),kv(),cw(e),pw()]}}const yw={tokenize:Cw,partial:!0},xh={tokenize:Ew,partial:!0},kh={tokenize:Tw,partial:!0},vh={tokenize:zw,partial:!0},xw={tokenize:Pw,partial:!0},wh={name:"wwwAutolink",tokenize:ww,previous:Ch},Sh={name:"protocolAutolink",tokenize:Sw,previous:Eh},Nn={name:"emailAutolink",tokenize:vw,previous:Th},Cn={};function kw(){return{text:Cn}}let ut=48;for(;ut<123;)Cn[ut]=Nn,ut++,ut===58?ut=65:ut===91&&(ut=97);Cn[43]=Nn;Cn[45]=Nn;Cn[46]=Nn;Cn[95]=Nn;Cn[72]=[Nn,Sh];Cn[104]=[Nn,Sh];Cn[87]=[Nn,wh];Cn[119]=[Nn,wh];function vw(e,n,t){const r=this;let i,l;return o;function o(c){return!Mu(c)||!Th.call(r,r.previous)||Ga(r.events)?t(c):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),u(c))}function u(c){return Mu(c)?(e.consume(c),u):c===64?(e.consume(c),a):t(c)}function a(c){return c===46?e.check(xw,f,s)(c):c===45||c===95||Ie(c)?(l=!0,e.consume(c),a):f(c)}function s(c){return e.consume(c),i=!0,a}function f(c){return l&&i&&Re(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),n(c)):t(c)}}function ww(e,n,t){const r=this;return i;function i(o){return o!==87&&o!==119||!Ch.call(r,r.previous)||Ga(r.events)?t(o):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(yw,e.attempt(xh,e.attempt(kh,l),t),t)(o))}function l(o){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),n(o)}}function Sw(e,n,t){const r=this;let i="",l=!1;return o;function o(c){return(c===72||c===104)&&Eh.call(r,r.previous)&&!Ga(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(c),e.consume(c),u):t(c)}function u(c){if(Re(c)&&i.length<5)return i+=String.fromCodePoint(c),e.consume(c),u;if(c===58){const p=i.toLowerCase();if(p==="http"||p==="https")return e.consume(c),a}return t(c)}function a(c){return c===47?(e.consume(c),l?s:(l=!0,a)):t(c)}function s(c){return c===null||xl(c)||J(c)||wt(c)||Ol(c)?t(c):e.attempt(xh,e.attempt(kh,f),t)(c)}function f(c){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),n(c)}}function Cw(e,n,t){let r=0;return i;function i(o){return(o===87||o===119)&&r<3?(r++,e.consume(o),i):o===46&&r===3?(e.consume(o),l):t(o)}function l(o){return o===null?t(o):n(o)}}function Ew(e,n,t){let r,i,l;return o;function o(s){return s===46||s===95?e.check(vh,a,u)(s):s===null||J(s)||wt(s)||s!==45&&Ol(s)?a(s):(l=!0,e.consume(s),o)}function u(s){return s===95?r=!0:(i=r,r=void 0),e.consume(s),o}function a(s){return i||r||!l?t(s):n(s)}}function Tw(e,n){let t=0,r=0;return i;function i(o){return o===40?(t++,e.consume(o),i):o===41&&r<t?l(o):o===33||o===34||o===38||o===39||o===41||o===42||o===44||o===46||o===58||o===59||o===60||o===63||o===93||o===95||o===126?e.check(vh,n,l)(o):o===null||J(o)||wt(o)?n(o):(e.consume(o),i)}function l(o){return o===41&&r++,e.consume(o),i}}function zw(e,n,t){return r;function r(u){return u===33||u===34||u===39||u===41||u===42||u===44||u===46||u===58||u===59||u===63||u===95||u===126?(e.consume(u),r):u===38?(e.consume(u),l):u===93?(e.consume(u),i):u===60||u===null||J(u)||wt(u)?n(u):t(u)}function i(u){return u===null||u===40||u===91||J(u)||wt(u)?n(u):r(u)}function l(u){return Re(u)?o(u):t(u)}function o(u){return u===59?(e.consume(u),r):Re(u)?(e.consume(u),o):t(u)}}function Pw(e,n,t){return r;function r(l){return e.consume(l),i}function i(l){return Ie(l)?t(l):n(l)}}function Ch(e){return e===null||e===40||e===42||e===95||e===91||e===93||e===126||J(e)}function Eh(e){return!Re(e)}function Th(e){return!(e===47||Mu(e))}function Mu(e){return e===43||e===45||e===46||e===95||Ie(e)}function Ga(e){let n=e.length,t=!1;for(;n--;){const r=e[n][1];if((r.type==="labelLink"||r.type==="labelImage")&&!r._balanced){t=!0;break}if(r._gfmAutolinkLiteralWalkedInto){t=!1;break}}return e.length>0&&!t&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),t}const Iw={tokenize:Nw,partial:!0};function _w(){return{document:{[91]:{name:"gfmFootnoteDefinition",tokenize:Aw,continuation:{tokenize:Dw},exit:jw}},text:{[91]:{name:"gfmFootnoteCall",tokenize:Rw},[93]:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:bw,resolveTo:Lw}}}}function bw(e,n,t){const r=this;let i=r.events.length;const l=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let o;for(;i--;){const a=r.events[i][1];if(a.type==="labelImage"){o=a;break}if(a.type==="gfmFootnoteCall"||a.type==="labelLink"||a.type==="label"||a.type==="image"||a.type==="link")break}return u;function u(a){if(!o||!o._balanced)return t(a);const s=hn(r.sliceSerialize({start:o.end,end:r.now()}));return s.codePointAt(0)!==94||!l.includes(s.slice(1))?t(a):(e.enter("gfmFootnoteCallLabelMarker"),e.consume(a),e.exit("gfmFootnoteCallLabelMarker"),n(a))}}function Lw(e,n){let t=e.length;for(;t--;)if(e[t][1].type==="labelImage"&&e[t][0]==="enter"){e[t][1];break}e[t+1][1].type="data",e[t+3][1].type="gfmFootnoteCallLabelMarker";const r={type:"gfmFootnoteCall",start:Object.assign({},e[t+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[t+3][1].end),end:Object.assign({},e[t+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;const l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},u=[e[t+1],e[t+2],["enter",r,n],e[t+3],e[t+4],["enter",i,n],["exit",i,n],["enter",l,n],["enter",o,n],["exit",o,n],["exit",l,n],e[e.length-2],e[e.length-1],["exit",r,n]];return e.splice(t,e.length-t+1,...u),e}function Rw(e,n,t){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l=0,o;return u;function u(c){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(c),e.exit("gfmFootnoteCallLabelMarker"),a}function a(c){return c!==94?t(c):(e.enter("gfmFootnoteCallMarker"),e.consume(c),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(c){if(l>999||c===93&&!o||c===null||c===91||J(c))return t(c);if(c===93){e.exit("chunkString");const p=e.exit("gfmFootnoteCallString");return i.includes(hn(r.sliceSerialize(p)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(c),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),n):t(c)}return J(c)||(o=!0),l++,e.consume(c),c===92?f:s}function f(c){return c===91||c===92||c===93?(e.consume(c),l++,s):s(c)}}function Aw(e,n,t){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l,o=0,u;return a;function a(g){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionLabelMarker"),s}function s(g){return g===94?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",f):t(g)}function f(g){if(o>999||g===93&&!u||g===null||g===91||J(g))return t(g);if(g===93){e.exit("chunkString");const v=e.exit("gfmFootnoteDefinitionLabelString");return l=hn(r.sliceSerialize(v)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}return J(g)||(u=!0),o++,e.consume(g),g===92?c:f}function c(g){return g===91||g===92||g===93?(e.consume(g),o++,f):f(g)}function p(g){return g===58?(e.enter("definitionMarker"),e.consume(g),e.exit("definitionMarker"),i.includes(l)||i.push(l),Q(e,d,"gfmFootnoteDefinitionWhitespace")):t(g)}function d(g){return n(g)}}function Dw(e,n,t){return e.check(ai,n,e.attempt(Iw,n,t))}function jw(e){e.exit("gfmFootnoteDefinition")}function Nw(e,n,t){const r=this;return Q(e,i,"gfmFootnoteDefinitionIndent",4+1);function i(l){const o=r.events[r.events.length-1];return o&&o[1].type==="gfmFootnoteDefinitionIndent"&&o[2].sliceSerialize(o[1],!0).length===4?n(l):t(l)}}function Fw(e){let t=(e||{}).singleTilde;const r={name:"strikethrough",tokenize:l,resolveAll:i};return t==null&&(t=!0),{text:{[126]:r},insideSpan:{null:[r]},attentionMarkers:{null:[126]}};function i(o,u){let a=-1;for(;++a<o.length;)if(o[a][0]==="enter"&&o[a][1].type==="strikethroughSequenceTemporary"&&o[a][1]._close){let s=a;for(;s--;)if(o[s][0]==="exit"&&o[s][1].type==="strikethroughSequenceTemporary"&&o[s][1]._open&&o[a][1].end.offset-o[a][1].start.offset===o[s][1].end.offset-o[s][1].start.offset){o[a][1].type="strikethroughSequence",o[s][1].type="strikethroughSequence";const f={type:"strikethrough",start:Object.assign({},o[s][1].start),end:Object.assign({},o[a][1].end)},c={type:"strikethroughText",start:Object.assign({},o[s][1].end),end:Object.assign({},o[a][1].start)},p=[["enter",f,u],["enter",o[s][1],u],["exit",o[s][1],u],["enter",c,u]],d=u.parser.constructs.insideSpan.null;d&&Ye(p,p.length,0,Bl(d,o.slice(s+1,a),u)),Ye(p,p.length,0,[["exit",c,u],["enter",o[a][1],u],["exit",o[a][1],u],["exit",f,u]]),Ye(o,s-1,a-s+3,p),a=s+p.length-2;break}}for(a=-1;++a<o.length;)o[a][1].type==="strikethroughSequenceTemporary"&&(o[a][1].type="data");return o}function l(o,u,a){const s=this.previous,f=this.events;let c=0;return p;function p(g){return s===126&&f[f.length-1][1].type!=="characterEscape"?a(g):(o.enter("strikethroughSequenceTemporary"),d(g))}function d(g){const v=rr(s);if(g===126)return c>1?a(g):(o.consume(g),c++,d);if(c<2&&!t)return a(g);const E=o.exit("strikethroughSequenceTemporary"),h=rr(g);return E._open=!h||h===2&&!!v,E._close=!v||v===2&&!!h,u(g)}}}class Mw{constructor(){this.map=[]}add(n,t,r){Ow(this,n,t,r)}consume(n){if(this.map.sort(function(l,o){return l[0]-o[0]}),this.map.length===0)return;let t=this.map.length;const r=[];for(;t>0;)t-=1,r.push(n.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),n.length=this.map[t][0];r.push(n.slice()),n.length=0;let i=r.pop();for(;i;){for(const l of i)n.push(l);i=r.pop()}this.map.length=0}}function Ow(e,n,t,r){let i=0;if(!(t===0&&r.length===0)){for(;i<e.map.length;){if(e.map[i][0]===n){e.map[i][1]+=t,e.map[i][2].push(...r);return}i+=1}e.map.push([n,t,r])}}function Bw(e,n){let t=!1;const r=[];for(;n<e.length;){const i=e[n];if(t){if(i[0]==="enter")i[1].type==="tableContent"&&r.push(e[n+1][1].type==="tableDelimiterMarker"?"left":"none");else if(i[1].type==="tableContent"){if(e[n-1][1].type==="tableDelimiterMarker"){const l=r.length-1;r[l]=r[l]==="left"?"center":"right"}}else if(i[1].type==="tableDelimiterRow")break}else i[0]==="enter"&&i[1].type==="tableDelimiterRow"&&(t=!0);n+=1}return r}function Uw(){return{flow:{null:{name:"table",tokenize:$w,resolveAll:Hw}}}}function $w(e,n,t){const r=this;let i=0,l=0,o;return u;function u(C){let D=r.events.length-1;for(;D>-1;){const ee=r.events[D][1].type;if(ee==="lineEnding"||ee==="linePrefix")D--;else break}const N=D>-1?r.events[D][1].type:null,q=N==="tableHead"||N==="tableRow"?S:a;return q===S&&r.parser.lazy[r.now().line]?t(C):q(C)}function a(C){return e.enter("tableHead"),e.enter("tableRow"),s(C)}function s(C){return C===124||(o=!0,l+=1),f(C)}function f(C){return C===null?t(C):F(C)?l>1?(l=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(C),e.exit("lineEnding"),d):t(C):$(C)?Q(e,f,"whitespace")(C):(l+=1,o&&(o=!1,i+=1),C===124?(e.enter("tableCellDivider"),e.consume(C),e.exit("tableCellDivider"),o=!0,f):(e.enter("data"),c(C)))}function c(C){return C===null||C===124||J(C)?(e.exit("data"),f(C)):(e.consume(C),C===92?p:c)}function p(C){return C===92||C===124?(e.consume(C),c):c(C)}function d(C){return r.interrupt=!1,r.parser.lazy[r.now().line]?t(C):(e.enter("tableDelimiterRow"),o=!1,$(C)?Q(e,g,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(C):g(C))}function g(C){return C===45||C===58?E(C):C===124?(o=!0,e.enter("tableCellDivider"),e.consume(C),e.exit("tableCellDivider"),v):P(C)}function v(C){return $(C)?Q(e,E,"whitespace")(C):E(C)}function E(C){return C===58?(l+=1,o=!0,e.enter("tableDelimiterMarker"),e.consume(C),e.exit("tableDelimiterMarker"),h):C===45?(l+=1,h(C)):C===null||F(C)?T(C):P(C)}function h(C){return C===45?(e.enter("tableDelimiterFiller"),m(C)):P(C)}function m(C){return C===45?(e.consume(C),m):C===58?(o=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(C),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(C))}function y(C){return $(C)?Q(e,T,"whitespace")(C):T(C)}function T(C){return C===124?g(C):C===null||F(C)?!o||i!==l?P(C):(e.exit("tableDelimiterRow"),e.exit("tableHead"),n(C)):P(C)}function P(C){return t(C)}function S(C){return e.enter("tableRow"),_(C)}function _(C){return C===124?(e.enter("tableCellDivider"),e.consume(C),e.exit("tableCellDivider"),_):C===null||F(C)?(e.exit("tableRow"),n(C)):$(C)?Q(e,_,"whitespace")(C):(e.enter("data"),L(C))}function L(C){return C===null||C===124||J(C)?(e.exit("data"),_(C)):(e.consume(C),C===92?M:L)}function M(C){return C===92||C===124?(e.consume(C),L):L(C)}}function Hw(e,n){let t=-1,r=!0,i=0,l=[0,0,0,0],o=[0,0,0,0],u=!1,a=0,s,f,c;const p=new Mw;for(;++t<e.length;){const d=e[t],g=d[1];d[0]==="enter"?g.type==="tableHead"?(u=!1,a!==0&&(uf(p,n,a,s,f),f=void 0,a=0),s={type:"table",start:Object.assign({},g.start),end:Object.assign({},g.end)},p.add(t,0,[["enter",s,n]])):g.type==="tableRow"||g.type==="tableDelimiterRow"?(r=!0,c=void 0,l=[0,0,0,0],o=[0,t+1,0,0],u&&(u=!1,f={type:"tableBody",start:Object.assign({},g.start),end:Object.assign({},g.end)},p.add(t,0,[["enter",f,n]])),i=g.type==="tableDelimiterRow"?2:f?3:1):i&&(g.type==="data"||g.type==="tableDelimiterMarker"||g.type==="tableDelimiterFiller")?(r=!1,o[2]===0&&(l[1]!==0&&(o[0]=o[1],c=Ri(p,n,l,i,void 0,c),l=[0,0,0,0]),o[2]=t)):g.type==="tableCellDivider"&&(r?r=!1:(l[1]!==0&&(o[0]=o[1],c=Ri(p,n,l,i,void 0,c)),l=o,o=[l[1],t,0,0])):g.type==="tableHead"?(u=!0,a=t):g.type==="tableRow"||g.type==="tableDelimiterRow"?(a=t,l[1]!==0?(o[0]=o[1],c=Ri(p,n,l,i,t,c)):o[1]!==0&&(c=Ri(p,n,o,i,t,c)),i=0):i&&(g.type==="data"||g.type==="tableDelimiterMarker"||g.type==="tableDelimiterFiller")&&(o[3]=t)}for(a!==0&&uf(p,n,a,s,f),p.consume(n.events),t=-1;++t<n.events.length;){const d=n.events[t];d[0]==="enter"&&d[1].type==="table"&&(d[1]._align=Bw(n.events,t))}return e}function Ri(e,n,t,r,i,l){const o=r===1?"tableHeader":r===2?"tableDelimiter":"tableData",u="tableContent";t[0]!==0&&(l.end=Object.assign({},bt(n.events,t[0])),e.add(t[0],0,[["exit",l,n]]));const a=bt(n.events,t[1]);if(l={type:o,start:Object.assign({},a),end:Object.assign({},a)},e.add(t[1],0,[["enter",l,n]]),t[2]!==0){const s=bt(n.events,t[2]),f=bt(n.events,t[3]),c={type:u,start:Object.assign({},s),end:Object.assign({},f)};if(e.add(t[2],0,[["enter",c,n]]),r!==2){const p=n.events[t[2]],d=n.events[t[3]];if(p[1].end=Object.assign({},d[1].end),p[1].type="chunkText",p[1].contentType="text",t[3]>t[2]+1){const g=t[2]+1,v=t[3]-t[2]-1;e.add(g,v,[])}}e.add(t[3]+1,0,[["exit",c,n]])}return i!==void 0&&(l.end=Object.assign({},bt(n.events,i)),e.add(i,0,[["exit",l,n]]),l=void 0),l}function uf(e,n,t,r,i){const l=[],o=bt(n.events,t);i&&(i.end=Object.assign({},o),l.push(["exit",i,n])),r.end=Object.assign({},o),l.push(["exit",r,n]),e.add(t+1,0,l)}function bt(e,n){const t=e[n],r=t[0]==="enter"?"start":"end";return t[1][r]}const Vw={name:"tasklistCheck",tokenize:Qw};function Ww(){return{text:{[91]:Vw}}}function Qw(e,n,t){const r=this;return i;function i(a){return r.previous!==null||!r._gfmTasklistFirstContentOfListItem?t(a):(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(a),e.exit("taskListCheckMarker"),l)}function l(a){return J(a)?(e.enter("taskListCheckValueUnchecked"),e.consume(a),e.exit("taskListCheckValueUnchecked"),o):a===88||a===120?(e.enter("taskListCheckValueChecked"),e.consume(a),e.exit("taskListCheckValueChecked"),o):t(a)}function o(a){return a===93?(e.enter("taskListCheckMarker"),e.consume(a),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),u):t(a)}function u(a){return F(a)?n(a):$(a)?e.check({tokenize:qw},n,t)(a):t(a)}}function qw(e,n,t){return Q(e,r,"whitespace");function r(i){return i===null?t(i):n(i)}}function Yw(e){return jd([kw(),_w(),Fw(e),Uw(),Ww()])}const Kw={};function Xw(e){const n=this,t=e||Kw,r=n.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),l=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),o=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push(Yw(t)),l.push(mw()),o.push(gw(t))}const Gw=()=>{const[e,n]=le.useState([]),[t,r]=le.useState(""),[i,l]=le.useState(!1),[o,u]=le.useState(!1),a=le.useRef(null),s=le.useRef(null),f=()=>{var E;(E=a.current)==null||E.scrollIntoView({behavior:"smooth"})};le.useEffect(f,[e]);const c=()=>{n([])},p=()=>{const E=e.map(T=>`${T.sender==="user"?"Você":"Bot"}: ${T.text}`).join(`
`),h=new Blob([E],{type:"text/plain"}),m=URL.createObjectURL(h),y=document.createElement("a");y.href=m,y.download="conversacao_chat.txt",document.body.appendChild(y),y.click(),document.body.removeChild(y),URL.revokeObjectURL(m)},d=async()=>{if(t.trim()==="")return;const E=t,h={text:E,sender:"user"};n(m=>[...m,h]),r(""),l(!0);try{const m=e.map(P=>({user:P.sender==="user"?P.text:"",assistant:P.sender==="bot"?P.text:""})).filter(P=>P.user||P.assistant),y=await fetch("http://127.0.0.1:8000/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:E,chat_history:m})});if(!y.ok)throw new Error(`HTTP error! status: ${y.status}`);const T=await y.json();if(T&&T.resposta_formatada){const P={text:T.resposta_formatada,sender:"bot"};n(S=>[...S,P])}else throw new Error("Invalid response format: resposta_formatada is missing or null.")}catch(m){console.error("Error sending message:",m),n(y=>[...y,{text:"Hmm, parece que estou com problemas de conexão. Podes tentar novamente? 🔌",sender:"bot"}])}finally{l(!1)}},g=E=>{const h=E.target.value;r(h),h.length>0&&!o&&u(!0),s.current&&clearTimeout(s.current),s.current=setTimeout(()=>{u(!1)},1e3),h.length===0&&(u(!1),s.current&&clearTimeout(s.current))},v=E=>{E.key==="Enter"&&(d(),u(!1),s.current&&clearTimeout(s.current))};return w.jsxs("div",{style:{display:"flex",flexDirection:"column",height:"100%",backgroundColor:"#030712"},children:[w.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"20px 24px",borderBottom:"1px solid #1f2937",backgroundColor:"#111827"},children:[w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[w.jsx("div",{style:{width:"32px",height:"32px",backgroundColor:"#10b981",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center"},children:w.jsx("span",{style:{color:"#111827",fontSize:"14px"},children:"💬"})}),w.jsxs("div",{style:{display:"flex",flexDirection:"column"},children:[w.jsx("h2",{style:{fontSize:"20px",fontWeight:"bold",color:"#ffffff",margin:0},children:"Chat com Documentos"}),o&&w.jsx("span",{style:{fontSize:"12px",color:"#10b981",marginTop:"2px"},children:"A escrever... ✍️"})]})]}),w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[w.jsx("button",{onClick:p,title:"Exportar Conversa",style:{padding:"8px",color:"#9ca3af",backgroundColor:"transparent",border:"none",borderRadius:"8px",cursor:"pointer",transition:"all 0.2s"},onMouseEnter:E=>{E.currentTarget.style.color="#ffffff",E.currentTarget.style.backgroundColor="#1f2937"},onMouseLeave:E=>{E.currentTarget.style.color="#9ca3af",E.currentTarget.style.backgroundColor="transparent"},children:w.jsx("span",{style:{fontSize:"14px"},children:"💾"})}),w.jsx("button",{onClick:c,title:"Limpar Histórico",style:{padding:"8px",color:"#9ca3af",backgroundColor:"transparent",border:"none",borderRadius:"8px",cursor:"pointer",transition:"all 0.2s"},onMouseEnter:E=>{E.currentTarget.style.color="#ffffff",E.currentTarget.style.backgroundColor="#1f2937"},onMouseLeave:E=>{E.currentTarget.style.color="#9ca3af",E.currentTarget.style.backgroundColor="transparent"},children:w.jsx("span",{style:{fontSize:"14px"},children:"🗑️"})})]})]}),w.jsx("div",{style:{flex:1,overflowY:"auto",padding:"24px"},children:e.length===0?w.jsx("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:w.jsx("div",{style:{textAlign:"center"},children:w.jsxs("div",{style:{backgroundColor:"#111827",borderRadius:"16px",border:"1px solid #1f2937",padding:"32px",maxWidth:"400px"},children:[w.jsx("div",{style:{width:"64px",height:"64px",backgroundColor:"#1f2937",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 16px"},children:w.jsx("span",{style:{color:"#10b981",fontSize:"24px"},children:"💬"})}),w.jsx("h3",{style:{fontSize:"18px",fontWeight:"500",color:"#d1d5db",marginBottom:"8px"},children:"Olá! 👋 Sou o teu assistente de documentos"}),w.jsx("p",{style:{color:"#6b7280",marginBottom:"16px"},children:"Podes fazer-me qualquer pergunta sobre os documentos que carregaste, ou simplesmente conversar comigo! 😊"}),w.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"8px",marginTop:"16px"},children:[w.jsx("p",{style:{fontSize:"14px",color:"#9ca3af",marginBottom:"8px"},children:"Exemplos do que podes perguntar:"}),w.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:["Olá! Como estás?","Que documentos tens?","Resume o documento X","Explica-me sobre..."].map((E,h)=>w.jsx("button",{onClick:()=>r(E),style:{padding:"6px 12px",backgroundColor:"#1f2937",border:"1px solid #374151",borderRadius:"16px",color:"#d1d5db",fontSize:"12px",cursor:"pointer",transition:"all 0.2s"},onMouseEnter:m=>{m.currentTarget.style.backgroundColor="#374151",m.currentTarget.style.borderColor="#10b981"},onMouseLeave:m=>{m.currentTarget.style.backgroundColor="#1f2937",m.currentTarget.style.borderColor="#374151"},children:E},h))})]})]})})}):w.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"16px",maxWidth:"800px",margin:"0 auto"},children:[e.map((E,h)=>w.jsx("div",{style:{display:"flex",justifyContent:E.sender==="user"?"flex-end":"flex-start"},children:w.jsx("div",{style:{maxWidth:E.sender==="user"?"70%":"85%",padding:E.sender==="user"?"12px 16px":"20px 24px",borderRadius:"12px",backgroundColor:E.sender==="user"?"#10b981":"#111827",color:E.sender==="user"?"#111827":"#f9fafb",border:E.sender==="user"?"none":"1px solid #1f2937",boxShadow:E.sender==="user"?"none":"0 2px 8px rgba(0, 0, 0, 0.1)"},children:w.jsx("div",{className:"chat-message",children:w.jsx(Fk,{remarkPlugins:[Xw],children:E.text})})})},h)),i&&w.jsx("div",{style:{display:"flex",justifyContent:"flex-start"},children:w.jsx("div",{style:{backgroundColor:"#111827",color:"#f9fafb",border:"1px solid #1f2937",padding:"12px 16px",borderRadius:"12px"},children:w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[w.jsxs("div",{style:{display:"flex",gap:"4px"},children:[w.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#10b981",borderRadius:"50%",animation:"bounce 1.4s infinite ease-in-out"}}),w.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#10b981",borderRadius:"50%",animation:"bounce 1.4s infinite ease-in-out 0.16s"}}),w.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#10b981",borderRadius:"50%",animation:"bounce 1.4s infinite ease-in-out 0.32s"}})]}),w.jsx("span",{style:{fontSize:"14px",color:"#9ca3af"},children:"A pensar... 🤔"})]})})}),w.jsx("div",{ref:a})]})}),w.jsx("div",{style:{padding:"24px",borderTop:"1px solid #1f2937",backgroundColor:"#111827"},children:w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"16px",maxWidth:"800px",margin:"0 auto"},children:[w.jsx("input",{type:"text",value:t,onChange:g,onKeyDown:v,placeholder:"Escreve aqui a tua mensagem... 💬",disabled:i,style:{flex:1,padding:"12px 16px",backgroundColor:"#1f2937",border:"1px solid #374151",borderRadius:"8px",color:"#f9fafb",fontSize:"16px",outline:"none",opacity:i?.5:1,cursor:i?"not-allowed":"text"},onFocus:E=>E.currentTarget.style.borderColor="#10b981",onBlur:E=>E.currentTarget.style.borderColor="#374151"}),w.jsxs("button",{onClick:d,disabled:i||t.trim()==="",style:{display:"flex",alignItems:"center",gap:"8px",padding:"12px 24px",backgroundColor:i||t.trim()===""?"#374151":"#10b981",color:i||t.trim()===""?"#6b7280":"#111827",borderRadius:"8px",fontWeight:"500",border:"none",cursor:i||t.trim()===""?"not-allowed":"pointer",transition:"background-color 0.2s"},onMouseEnter:E=>{!i&&t.trim()!==""&&(E.currentTarget.style.backgroundColor="#059669")},onMouseLeave:E=>{!i&&t.trim()!==""&&(E.currentTarget.style.backgroundColor="#10b981")},children:[w.jsx("span",{style:{fontSize:"14px"},children:"📤"}),w.jsx("span",{children:"Enviar"})]})]})})]})};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zw=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Jw=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(n,t,r)=>r?r.toUpperCase():t.toLowerCase()),af=e=>{const n=Jw(e);return n.charAt(0).toUpperCase()+n.slice(1)},zh=(...e)=>e.filter((n,t,r)=>!!n&&n.trim()!==""&&r.indexOf(n)===t).join(" ").trim(),eS=e=>{for(const n in e)if(n.startsWith("aria-")||n==="role"||n==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var nS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tS=le.forwardRef(({color:e="currentColor",size:n=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:i="",children:l,iconNode:o,...u},a)=>le.createElement("svg",{ref:a,...nS,width:n,height:n,stroke:e,strokeWidth:r?Number(t)*24/Number(n):t,className:zh("lucide",i),...!l&&!eS(u)&&{"aria-hidden":"true"},...u},[...o.map(([s,f])=>le.createElement(s,f)),...Array.isArray(l)?l:[l]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ci=(e,n)=>{const t=le.forwardRef(({className:r,...i},l)=>le.createElement(tS,{ref:l,iconNode:n,className:zh(`lucide-${Zw(af(e))}`,`lucide-${e}`,r),...i}));return t.displayName=af(e),t};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rS=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],iS=ci("check",rS);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lS=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]],sf=ci("file",lS);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oS=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],cf=ci("loader-circle",oS);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uS=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],Ro=ci("upload",uS);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aS=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],ff=ci("x",aS),sS=()=>{const[e,n]=le.useState(null),[t,r]=le.useState(""),[i,l]=le.useState(!1),o=a=>{a.target.files&&a.target.files[0]?(n(a.target.files[0]),r("")):n(null)},u=async()=>{if(!e){r("Por favor, selecione um arquivo PDF para upload.");return}if(e.type!=="application/pdf"){r("Apenas arquivos PDF são permitidos.");return}l(!0),r("Enviando e processando o arquivo...");const a=new FormData;a.append("file",e);try{const s=await fetch("http://127.0.0.1:8000/api/upload_pdf",{method:"POST",body:a});if(s.ok){const f=await s.json();r(f.message||"Arquivo enviado e processado com sucesso!"),n(null)}else{const f=await s.json();r(f.detail||"Erro ao enviar o arquivo.")}}catch(s){console.error("Erro ao fazer upload:",s),r("Erro de conexão. Tente novamente.")}finally{l(!1)}};return w.jsxs("div",{style:{display:"flex",flexDirection:"column",height:"100%",backgroundColor:"#030712"},children:[w.jsx("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"20px 24px",borderBottom:"1px solid #1f2937",backgroundColor:"#111827"},children:w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[w.jsx("div",{style:{width:"32px",height:"32px",backgroundColor:"#10b981",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center"},children:w.jsx(Ro,{size:16,color:"#111827"})}),w.jsxs("div",{style:{display:"flex",flexDirection:"column"},children:[w.jsx("h2",{style:{fontSize:"20px",fontWeight:"bold",color:"#ffffff",margin:0},children:"Upload de Documentos"}),w.jsx("span",{style:{fontSize:"14px",color:"#9ca3af"},children:"Carrega os teus PDFs para análise"})]})]})}),w.jsx("div",{style:{flex:1,padding:"32px",overflowY:"auto"},children:w.jsxs("div",{style:{maxWidth:"600px",margin:"0 auto"},children:[w.jsxs("div",{style:{backgroundColor:"#111827",border:"1px solid #1f2937",borderRadius:"12px",padding:"32px",textAlign:"center"},children:[w.jsx("div",{style:{width:"80px",height:"80px",backgroundColor:"#1f2937",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 24px"},children:w.jsx(Ro,{size:32,color:"#10b981"})}),w.jsx("h3",{style:{fontSize:"24px",fontWeight:"bold",color:"#ffffff",marginBottom:"8px"},children:"Carrega os teus documentos"}),w.jsx("p",{style:{color:"#9ca3af",marginBottom:"32px",fontSize:"16px"},children:"Seleciona ficheiros PDF para análise e chat inteligente"}),w.jsxs("div",{style:{marginBottom:"24px"},children:[w.jsx("input",{type:"file",accept:".pdf",onChange:o,style:{display:"none"},id:"file-upload"}),w.jsxs("label",{htmlFor:"file-upload",style:{display:"inline-flex",alignItems:"center",gap:"12px",padding:"16px 24px",backgroundColor:"#10b981",color:"#111827",borderRadius:"8px",cursor:"pointer",fontWeight:"600",fontSize:"16px",border:"none",transition:"all 0.2s"},onMouseEnter:a=>{a.currentTarget.style.backgroundColor="#059669"},onMouseLeave:a=>{a.currentTarget.style.backgroundColor="#10b981"},children:[w.jsx(sf,{size:20}),"Escolher ficheiro PDF"]})]}),e&&w.jsxs("div",{style:{backgroundColor:"#1f2937",border:"1px solid #374151",borderRadius:"8px",padding:"16px",marginBottom:"24px",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[w.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[w.jsx(sf,{size:20,color:"#10b981"}),w.jsxs("div",{children:[w.jsx("p",{style:{color:"#ffffff",fontWeight:"500",margin:0},children:e.name}),w.jsxs("p",{style:{color:"#9ca3af",fontSize:"14px",margin:0},children:[(e.size/1024/1024).toFixed(2)," MB"]})]})]}),w.jsx("button",{onClick:()=>n(null),style:{backgroundColor:"transparent",border:"none",color:"#ef4444",cursor:"pointer",padding:"4px"},children:w.jsx(ff,{size:20})})]}),w.jsx("button",{onClick:u,disabled:!e||i,style:{width:"100%",padding:"16px",backgroundColor:e&&!i?"#10b981":"#374151",color:e&&!i?"#111827":"#9ca3af",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"600",cursor:e&&!i?"pointer":"not-allowed",display:"flex",alignItems:"center",justifyContent:"center",gap:"12px",transition:"all 0.2s"},children:i?w.jsxs(w.Fragment,{children:[w.jsx(cf,{size:20,className:"animate-spin"}),"A processar..."]}):w.jsxs(w.Fragment,{children:[w.jsx(Ro,{size:20}),"Carregar e processar"]})})]}),t&&w.jsxs("div",{style:{marginTop:"24px",padding:"16px",borderRadius:"12px",display:"flex",alignItems:"center",gap:"12px",backgroundColor:t.includes("sucesso")?"rgba(16, 185, 129, 0.2)":t.includes("Erro")?"rgba(239, 68, 68, 0.2)":"rgba(59, 130, 246, 0.2)",border:`1px solid ${t.includes("sucesso")?"#10b981":t.includes("Erro")?"#ef4444":"#3b82f6"}`,color:t.includes("sucesso")?"#10b981":t.includes("Erro")?"#ef4444":"#3b82f6"},children:[w.jsx("div",{style:{fontSize:"20px"},children:t.includes("sucesso")?w.jsx(iS,{}):t.includes("Erro")?w.jsx(ff,{}):w.jsx(cf,{className:"animate-spin"})}),w.jsx("span",{style:{fontWeight:"500"},children:t})]})]})})]})},cS=()=>{const[e,n]=le.useState(""),[t,r]=le.useState("https://your-api-domain.com/api/chat"),[i,l]=le.useState("https://your-domain.com/chatbot-embed.js"),[o,u]=le.useState({width:"350px",height:"450px",primaryColor:"#007bff"}),a=()=>{const f=`<!-- RAGPinecone Chatbot Embed -->
<div id="rag-chatbot-container"></div>
<script src="${i}"><\/script>
<script>
  // Inicializar o chatbot quando a página carregar
  document.addEventListener('DOMContentLoaded', function() {
    initRagChatbot('rag-chatbot-container', '${t}');
  });
<\/script>

<style>
  /* Personalização do chatbot */
  .rag-chatbot-widget {
    width: ${o.width} !important;
    height: ${o.height} !important;
  }

  .rag-chatbot-header {
    background-color: ${o.primaryColor} !important;
  }

  .rag-chatbot-input-area button {
    background-color: ${o.primaryColor} !important;
  }
</style>`;n(f)},s=()=>{navigator.clipboard.writeText(e),alert("Código copiado para a área de transferência!")};return w.jsxs("div",{style:{padding:"32px",maxWidth:"1200px",margin:"0 auto",color:"white"},children:[w.jsxs("div",{style:{marginBottom:"32px"},children:[w.jsx("h1",{style:{fontSize:"32px",fontWeight:"bold",marginBottom:"16px"},children:"Embed Chatbot"}),w.jsx("p",{style:{color:"#d1d5db",fontSize:"18px"},children:"Integre o chatbot RAGPinecone no seu website com apenas algumas linhas de código."})]}),w.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"32px",marginBottom:"32px"},children:[w.jsxs("div",{style:{backgroundColor:"#1f2937",borderRadius:"8px",padding:"24px"},children:[w.jsx("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"16px"},children:"Configuração"}),w.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[w.jsxs("div",{children:[w.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#d1d5db",marginBottom:"8px"},children:"URL do Script"}),w.jsx("input",{type:"text",value:i,onChange:f=>l(f.target.value),style:{width:"100%",padding:"8px 12px",backgroundColor:"#374151",border:"1px solid #4b5563",borderRadius:"6px",color:"white",outline:"none"},placeholder:"https://seusite.com/chatbot-embed.js"})]}),w.jsxs("div",{children:[w.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#d1d5db",marginBottom:"8px"},children:"URL da API"}),w.jsx("input",{type:"text",value:t,onChange:f=>r(f.target.value),style:{width:"100%",padding:"8px 12px",backgroundColor:"#374151",border:"1px solid #4b5563",borderRadius:"6px",color:"white",outline:"none"},placeholder:"https://seuapi.com/api/chat"})]}),w.jsxs("div",{children:[w.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#d1d5db",marginBottom:"8px"},children:"Cor Principal"}),w.jsx("input",{type:"color",value:o.primaryColor,onChange:f=>u({...o,primaryColor:f.target.value}),style:{width:"100%",height:"40px",backgroundColor:"#374151",border:"1px solid #4b5563",borderRadius:"6px",outline:"none"}})]}),w.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"},children:[w.jsxs("div",{children:[w.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#d1d5db",marginBottom:"8px"},children:"Largura"}),w.jsx("input",{type:"text",value:o.width,onChange:f=>u({...o,width:f.target.value}),style:{width:"100%",padding:"8px 12px",backgroundColor:"#374151",border:"1px solid #4b5563",borderRadius:"6px",color:"white",outline:"none"},placeholder:"350px"})]}),w.jsxs("div",{children:[w.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#d1d5db",marginBottom:"8px"},children:"Altura"}),w.jsx("input",{type:"text",value:o.height,onChange:f=>u({...o,height:f.target.value}),style:{width:"100%",padding:"8px 12px",backgroundColor:"#374151",border:"1px solid #4b5563",borderRadius:"6px",color:"white",outline:"none"},placeholder:"450px"})]})]}),w.jsx("button",{onClick:a,style:{width:"100%",backgroundColor:"#10b981",color:"white",fontWeight:"500",padding:"12px 16px",borderRadius:"6px",border:"none",cursor:"pointer",marginTop:"16px"},onMouseEnter:f=>f.currentTarget.style.backgroundColor="#059669",onMouseLeave:f=>f.currentTarget.style.backgroundColor="#10b981",children:"Gerar Código de Embed"})]})]}),w.jsxs("div",{style:{backgroundColor:"#1f2937",borderRadius:"8px",padding:"24px"},children:[w.jsx("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"16px"},children:"Pré-visualização"}),w.jsx("div",{style:{backgroundColor:"#374151",borderRadius:"8px",padding:"16px",height:"256px",display:"flex",alignItems:"center",justifyContent:"center"},children:w.jsxs("div",{style:{backgroundColor:"#4b5563",borderRadius:"8px",padding:"16px",border:`2px solid ${o.primaryColor}`,boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"},children:[w.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"},children:[w.jsx("div",{style:{width:"12px",height:"12px",borderRadius:"50%",backgroundColor:o.primaryColor,marginRight:"8px"}}),w.jsx("span",{style:{color:"white",fontSize:"14px"},children:"RAGPinecone Chat"})]}),w.jsxs("div",{style:{fontSize:"12px",color:"#9ca3af"},children:[o.width," × ",o.height]})]})})]})]}),e&&w.jsxs("div",{style:{backgroundColor:"#1f2937",borderRadius:"8px",padding:"24px",marginTop:"32px"},children:[w.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[w.jsx("h2",{style:{fontSize:"20px",fontWeight:"600"},children:"Código de Embed"}),w.jsx("button",{onClick:s,style:{backgroundColor:"#10b981",color:"white",fontWeight:"500",padding:"8px 16px",borderRadius:"6px",border:"none",cursor:"pointer"},onMouseEnter:f=>f.currentTarget.style.backgroundColor="#059669",onMouseLeave:f=>f.currentTarget.style.backgroundColor="#10b981",children:"Copiar Código"})]}),w.jsx("pre",{style:{backgroundColor:"#111827",borderRadius:"6px",padding:"16px",overflowX:"auto",fontSize:"14px",color:"#10b981"},children:w.jsx("code",{children:e})})]}),w.jsxs("div",{style:{backgroundColor:"#1f2937",borderRadius:"8px",padding:"24px",marginTop:"32px"},children:[w.jsx("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"16px"},children:"Instruções de Instalação"}),w.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"16px",color:"#d1d5db"},children:[w.jsxs("div",{children:[w.jsx("h3",{style:{fontWeight:"500",color:"white",marginBottom:"8px"},children:"1. Gerar e Copiar o Código"}),w.jsx("p",{children:'Use o botão "Gerar Código de Embed" e depois "Copiar Código".'})]}),w.jsxs("div",{children:[w.jsx("h3",{style:{fontWeight:"500",color:"white",marginBottom:"8px"},children:"2. Configurar URLs"}),w.jsx("p",{children:"Configure os URLs corretos nos campos acima antes de gerar o código."})]}),w.jsxs("div",{children:[w.jsx("h3",{style:{fontWeight:"500",color:"white",marginBottom:"8px"},children:"3. Integrar no Website"}),w.jsx("p",{children:"Cole o código HTML gerado na página onde pretende que o chatbot apareça."})]}),w.jsxs("div",{children:[w.jsx("h3",{style:{fontWeight:"500",color:"white",marginBottom:"8px"},children:"4. Ficheiro JavaScript"}),w.jsxs("p",{children:["Certifique-se de que o ficheiro ",w.jsx("span",{style:{backgroundColor:"#374151",padding:"2px 6px",borderRadius:"4px"},children:"chatbot-embed.js"})," está acessível no URL configurado."]})]})]})]})]})},fS=({activeSection:e,onSectionChange:n})=>{const t=[{id:"chat",label:"Chat com Documentos"},{id:"upload",label:"Upload de Ficheiros"},{id:"embed",label:"Embed Chatbot"},{id:"settings",label:"Configurações"}];return w.jsxs("div",{style:{width:"256px",height:"100vh",backgroundColor:"#111827",borderRight:"2px solid #10b981",display:"flex",flexDirection:"column",flexShrink:0,position:"relative",zIndex:1e3},children:[w.jsx("div",{style:{padding:"24px",borderBottom:"1px solid #374151"},children:w.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[w.jsx("div",{style:{width:"32px",height:"32px",backgroundColor:"#10b981",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center"},children:w.jsx("span",{style:{color:"white",fontWeight:"bold",fontSize:"14px"},children:"R"})}),w.jsxs("div",{style:{marginLeft:"12px"},children:[w.jsx("h1",{style:{color:"white",fontWeight:"600",fontSize:"18px",margin:0},children:"RAGPinecone"}),w.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"4px"},children:[w.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#4ade80",borderRadius:"50%"}}),w.jsx("span",{style:{color:"#9ca3af",fontSize:"14px",marginLeft:"8px"},children:"Online"})]})]})]})}),w.jsx("div",{style:{flex:1,padding:"16px 0",overflowY:"auto"},children:w.jsx("nav",{style:{padding:"0 12px"},children:t.map(r=>{const i=e===r.id;return w.jsxs("button",{onClick:()=>n(r.id),style:{width:"100%",display:"flex",alignItems:"center",padding:"12px",textAlign:"left",backgroundColor:i?"#1f2937":"transparent",color:i?"white":"#9ca3af",borderRadius:"8px",marginBottom:"4px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500"},onMouseEnter:l=>{i||(l.currentTarget.style.backgroundColor="#1f2937",l.currentTarget.style.color="white")},onMouseLeave:l=>{i||(l.currentTarget.style.backgroundColor="transparent",l.currentTarget.style.color="#9ca3af")},children:[w.jsxs("span",{style:{marginRight:"12px",fontSize:"16px"},children:[r.id==="chat"&&"💬",r.id==="upload"&&"📁",r.id==="embed"&&"🔗",r.id==="settings"&&"⚙️"]}),r.label]},r.id)})})}),w.jsx("div",{style:{borderTop:"1px solid #374151",padding:"16px"},children:w.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[w.jsx("div",{style:{width:"32px",height:"32px",backgroundColor:"#374151",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center"},children:w.jsx("span",{style:{color:"#d1d5db",fontSize:"12px"},children:"U"})}),w.jsxs("div",{style:{marginLeft:"12px"},children:[w.jsx("p",{style:{color:"white",fontSize:"14px",fontWeight:"500",margin:0},children:"Utilizador"}),w.jsx("p",{style:{color:"#9ca3af",fontSize:"12px",margin:0},children:"Admin"})]})]})})]})};function pS(){const[e,n]=le.useState("chat"),t=()=>{switch(e){case"chat":case"database":return w.jsx(Gw,{});case"upload":case"storage":return w.jsx(sS,{});case"embed":case"integrations":return w.jsx(cS,{});default:return w.jsxs("div",{className:"p-8",children:[w.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:e.charAt(0).toUpperCase()+e.slice(1)}),w.jsx("p",{className:"text-gray-300",children:"Esta funcionalidade está em desenvolvimento..."})]})}};return w.jsxs("div",{style:{display:"flex",height:"100vh",backgroundColor:"#111827"},children:[w.jsx(fS,{activeSection:e,onSectionChange:n}),w.jsx("main",{style:{flex:1,overflowY:"auto",backgroundColor:"#111827"},children:t()})]})}vd(document.getElementById("root")).render(w.jsx(le.StrictMode,{children:w.jsx(pS,{})}));
