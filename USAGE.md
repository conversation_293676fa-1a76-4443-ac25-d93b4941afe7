# 📖 Guia de Utilização - RAGPinecone

## 🚀 <PERSON><PERSON><PERSON>

### 1. Configuração Inicial

#### Backend (Python)
```bash
# Clonar o repositório
git clone <seu-repositorio>
cd RAGPinecone

# Instalar dependências Python
pip install -r requirements.txt

# Configurar variáveis de ambiente
cp .env.example .env
```

#### Frontend (React/TypeScript)
```bash
# Instalar dependências Node.js
npm install

# Verificar se tudo está configurado
npm run build
```

### 2. Configuração das Variáveis de Ambiente

Edite o ficheiro `.env` com as suas credenciais:

```env
# APIs Obrigatórias
GEMINI_API_KEY=sua_chave_gemini_aqui
PINECONE_API_KEY=sua_chave_pinecone_aqui
PINECONE_INDEX_NAME=seu_indice_pinecone

# Configurações Opcionais
PDF_FOLDER=./documentos
PINECONE_ENVIRONMENT=us-east-1-aws
```

### 3. Iniciar o Sistema

```bash
# Terminal 1: Backend (porta 8000)
uvicorn chat_with_docs:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Frontend (porta 5173)
npm run dev
```

Acesse: `http://localhost:5173`

## 🎯 Como Usar a Aplicação

### 📤 Upload de Documentos

1. **Aceder à secção Upload**
   - Clique em "Upload de Ficheiros" na sidebar
   - Ou navegue para a secção "Storage"

2. **Carregar PDFs**
   ```
   - Clique em "Escolher ficheiro PDF"
   - Selecione um ou mais ficheiros PDF
   - Clique em "Carregar e processar"
   ```

3. **Processamento Automático**
   - O sistema extrai texto e imagens
   - Aplica OCR se necessário
   - Indexa no Pinecone automaticamente
   - Mostra progresso em tempo real

### 💬 Chat com Documentos

1. **Iniciar Conversa**
   - Aceda à secção "Chat com Documentos"
   - Digite a sua pergunta na caixa de texto
   - Pressione Enter ou clique no botão enviar

2. **Tipos de Perguntas Suportadas**
   ```
   ✅ "Qual é o resumo do documento X?"
   ✅ "Encontra informações sobre [tópico]"
   ✅ "Compara os dados entre documentos A e B"
   ✅ "Lista os pontos principais sobre [assunto]"
   ```

3. **Funcionalidades do Chat**
   - **Histórico**: Mantém contexto da conversa
   - **Fontes**: Mostra documentos utilizados na resposta
   - **Markdown**: Respostas formatadas com listas e títulos
   - **Exportar**: Download da conversa completa

### 🔗 Integração Externa (Embed)

1. **Gerar Código Embed**
   - Aceda à secção "Embed Chatbot"
   - Configure URLs e personalização
   - Clique em "Gerar Código de Embed"

2. **Personalização Disponível**
   ```javascript
   {
     width: '350px',           // Largura do widget
     height: '450px',          // Altura do widget
     primaryColor: '#10b981',  // Cor principal
     apiUrl: 'https://...',    // URL da sua API
     scriptUrl: 'https://...'  // URL do script embed
   }
   ```

3. **Integrar no Website**
   ```html
   <!-- Copiar e colar no seu HTML -->
   <div id="rag-chatbot-container"></div>
   <script src="https://seu-dominio.com/chatbot-embed.js"></script>
   <script>
     document.addEventListener('DOMContentLoaded', function() {
       initRagChatbot('rag-chatbot-container', 'https://sua-api.com/api/chat');
     });
   </script>
   ```

## 🔧 Integração em Projetos Existentes

### Opção 1: Componente React Standalone

```tsx
import React from 'react';
import { ChatComponent } from './path/to/ChatComponent';

function MinhaAplicacao() {
  return (
    <div className="minha-app">
      <header>
        <h1>Minha Aplicação</h1>
      </header>

      <main>
        {/* Integrar o chat diretamente */}
        <section className="chat-section">
          <ChatComponent />
        </section>
      </main>
    </div>
  );
}
```

### Opção 2: Modal/Popup Controlado

```tsx
import React, { useState } from 'react';
import { ChatComponent } from './path/to/ChatComponent';

function MinhaAplicacao() {
  const [showChat, setShowChat] = useState(false);

  return (
    <div className="minha-app">
      {/* Botão para abrir chat */}
      <button
        onClick={() => setShowChat(true)}
        className="btn-chat-toggle"
      >
        💬 Consultar Documentos
      </button>

      {/* Modal do chat */}
      {showChat && (
        <div className="chat-modal-overlay">
          <div className="chat-modal">
            <button
              onClick={() => setShowChat(false)}
              className="btn-close"
            >
              ✕
            </button>
            <ChatComponent />
          </div>
        </div>
      )}
    </div>
  );
}
```

### Opção 3: Widget Flutuante

```tsx
import React, { useState } from 'react';
import { ChatComponent } from './path/to/ChatComponent';

function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Botão flutuante */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="chat-widget-button"
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          width: '60px',
          height: '60px',
          borderRadius: '50%',
          backgroundColor: '#10b981',
          border: 'none',
          color: 'white',
          fontSize: '24px',
          cursor: 'pointer',
          zIndex: 1000,
          boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
        }}
      >
        💬
      </button>

      {/* Widget do chat */}
      {isOpen && (
        <div
          className="chat-widget"
          style={{
            position: 'fixed',
            bottom: '90px',
            right: '20px',
            width: '350px',
            height: '500px',
            backgroundColor: '#111827',
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.4)',
            zIndex: 1000,
            overflow: 'hidden'
          }}
        >
          <ChatComponent />
        </div>
      )}
    </>
  );
}
```

## 🎨 Personalização e Estilos

### Esquema de Cores Padrão
```css
:root {
  --bg-primary: #030712;    /* Fundo principal */
  --bg-secondary: #111827;  /* Fundo secundário */
  --accent: #10b981;        /* Verde esmeralda */
  --text-primary: #f9fafb;  /* Texto principal */
  --text-secondary: #9ca3af; /* Texto secundário */
  --border: #1f2937;        /* Bordas */
}
```

### CSS para Modal Responsivo
```css
.chat-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.chat-modal {
  background: #111827;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  height: 90vh;
  max-height: 600px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0,0,0,0.5);
}

.btn-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 20px;
  cursor: pointer;
  z-index: 10;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-close:hover {
  background: rgba(255,255,255,0.1);
  color: #f9fafb;
}

/* Responsivo */
@media (max-width: 768px) {
  .chat-modal {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    border-radius: 0;
  }

  .chat-modal-overlay {
    padding: 0;
  }
}
```

## 🔧 Configuração Avançada

### Variáveis de Ambiente Completas
```env
# === APIs OBRIGATÓRIAS ===
GEMINI_API_KEY=sua_chave_gemini_aqui
PINECONE_API_KEY=sua_chave_pinecone_aqui
PINECONE_INDEX_NAME=ragpinecone-index

# === CONFIGURAÇÕES PINECONE ===
PINECONE_ENVIRONMENT=us-east-1-aws
PINECONE_DIMENSION=1536

# === CONFIGURAÇÕES DE DOCUMENTOS ===
PDF_FOLDER=./documentos
MAX_FILE_SIZE=50MB
ALLOWED_EXTENSIONS=pdf

# === CONFIGURAÇÕES DO CHAT ===
MAX_TOKENS=4000
TEMPERATURE=0.7
TOP_K=5

# === CONFIGURAÇÕES DO SERVIDOR ===
HOST=0.0.0.0
PORT=8000
CORS_ORIGINS=http://localhost:5173,https://seu-dominio.com
```

### Configuração de Produção

#### Backend (Docker)
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "chat_with_docs:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Frontend (Build)
```bash
# Build para produção
npm run build

# Servir ficheiros estáticos
npx serve -s dist -l 3000
```

## 📊 Monitorização e Logs

### Logs do Backend
```python
# Adicionar ao chat_with_docs.py
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# Usar nos endpoints
logger.info(f"Nova pergunta recebida: {question}")
logger.error(f"Erro no processamento: {str(e)}")
```

### Métricas Úteis
- Número de documentos processados
- Tempo de resposta das queries
- Uso de tokens da API Gemini
- Erros de conexão com Pinecone

## 🔒 Segurança e Boas Práticas

### Validação de Ficheiros
```python
# Validações implementadas
ALLOWED_EXTENSIONS = {'.pdf'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

def validate_file(file):
    # Verificar extensão
    if not file.filename.lower().endswith('.pdf'):
        raise ValueError("Apenas ficheiros PDF são permitidos")

    # Verificar tamanho
    if len(file.file.read()) > MAX_FILE_SIZE:
        raise ValueError("Ficheiro demasiado grande")
```

### CORS e Headers de Segurança
```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Apenas origens confiáveis
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

## 🚨 Resolução de Problemas

### Problemas Comuns

#### 1. Erro de Conexão com Pinecone
```bash
# Verificar conectividade
curl -H "Api-Key: SUA_CHAVE" https://api.pinecone.io/indexes

# Logs úteis
tail -f logs/pinecone.log
```

#### 2. Erro de API Gemini
```bash
# Verificar quota
curl -H "Authorization: Bearer SUA_CHAVE" \
     https://generativelanguage.googleapis.com/v1/models

# Verificar limites de rate
```

#### 3. Problemas de Upload
```bash
# Verificar permissões de pasta
ls -la ./documentos/

# Verificar espaço em disco
df -h
```

#### 4. Frontend não conecta ao Backend
```bash
# Verificar se backend está a correr
curl http://localhost:8000/health

# Verificar CORS
curl -H "Origin: http://localhost:5173" \
     -H "Access-Control-Request-Method: POST" \
     -X OPTIONS http://localhost:8000/api/chat
```

### Comandos de Debug
```bash
# Logs detalhados do backend
uvicorn chat_with_docs:app --reload --log-level debug

# Verificar dependências
pip list | grep -E "(pinecone|fastapi|google)"

# Testar API diretamente
curl -X POST http://localhost:8000/api/chat \
     -H "Content-Type: application/json" \
     -d '{"question": "teste", "chat_history": []}'
```
